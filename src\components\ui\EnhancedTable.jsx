import React, { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  Paper,
  Box,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Search as SearchIcon,
  FilterList as FilterIcon,
  GetApp as ExportIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: theme.spacing(2),
  border: '2px solid rgba(76, 175, 80, 0.1)',
  overflow: 'hidden',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)'
}));

const StyledTableHead = styled(TableHead)(({ theme }) => ({
  background: 'linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)',
  '& .MuiTableCell-head': {
    color: 'white',
    fontWeight: 700,
    fontSize: '0.95rem',
    borderBottom: 'none',
    padding: theme.spacing(2),
    position: 'relative',
    '&::after': {
      content: '""',
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      height: '2px',
      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)'
    }
  }
}));

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  cursor: 'pointer',
  '&:nth-of-type(even)': {
    backgroundColor: 'rgba(76, 175, 80, 0.02)'
  },
  '&:hover': {
    backgroundColor: 'rgba(76, 175, 80, 0.08)',
    transform: 'scale(1.001)',
    boxShadow: '0 4px 20px rgba(76, 175, 80, 0.15)',
    '& .MuiTableCell-root': {
      borderColor: 'rgba(76, 175, 80, 0.2)'
    }
  },
  '&:active': {
    transform: 'scale(0.999)'
  }
}));

const StyledTableCell = styled(TableCell)(({ theme }) => ({
  borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
  padding: theme.spacing(1.5, 2),
  fontSize: '0.9rem',
  transition: 'all 0.2s ease'
}));

const SearchContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  background: 'linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%)',
  borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(2)
}));

const EnhancedTable = ({
  columns = [],
  data = [],
  title,
  searchable = true,
  sortable = true,
  onRowClick,
  renderCell,
  actions,
  emptyMessage = "لا توجد بيانات للعرض"
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });

  // تصفية البيانات
  const filteredData = data.filter(row =>
    columns.some(column =>
      String(row[column.key] || '').toLowerCase().includes(searchTerm.toLowerCase())
    )
  );

  // ترتيب البيانات
  const sortedData = React.useMemo(() => {
    if (!sortConfig.key) return filteredData;

    return [...filteredData].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }, [filteredData, sortConfig]);

  const handleSort = (key) => {
    if (!sortable) return;
    
    setSortConfig(prevConfig => ({
      key,
      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const getCellContent = (row, column) => {
    if (renderCell && renderCell[column.key]) {
      return renderCell[column.key](row[column.key], row);
    }

    const value = row[column.key];
    
    if (column.type === 'chip') {
      return (
        <Chip
          label={value}
          size="small"
          color={column.chipColor || 'primary'}
          variant="outlined"
          sx={{ fontWeight: 500 }}
        />
      );
    }

    if (column.type === 'currency') {
      return (
        <Typography variant="body2" sx={{ fontWeight: 600, color: 'success.main' }}>
          {Number(value).toLocaleString()} ر.س
        </Typography>
      );
    }

    if (column.type === 'date') {
      return new Date(value).toLocaleDateString('ar-SA');
    }

    return value;
  };

  return (
    <StyledTableContainer component={Paper}>
      {(title || searchable || actions) && (
        <SearchContainer>
          <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', gap: 2 }}>
            {title && (
              <Typography variant="h6" sx={{ fontWeight: 600, color: 'text.primary' }}>
                {title}
              </Typography>
            )}
            
            {searchable && (
              <TextField
                placeholder="البحث..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
                sx={{ minWidth: 250 }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon sx={{ color: 'text.secondary' }} />
                    </InputAdornment>
                  ),
                  sx: {
                    borderRadius: 2,
                    '& fieldset': {
                      borderColor: 'rgba(76, 175, 80, 0.2)'
                    },
                    '&:hover fieldset': {
                      borderColor: 'primary.main'
                    }
                  }
                }}
              />
            )}
          </Box>

          {actions && (
            <Box sx={{ display: 'flex', gap: 1 }}>
              {actions}
            </Box>
          )}
        </SearchContainer>
      )}

      <Table>
        <StyledTableHead>
          <TableRow>
            {columns.map((column) => (
              <TableCell
                key={column.key}
                align={column.align || 'right'}
                sx={{ minWidth: column.minWidth }}
              >
                {sortable && column.sortable !== false ? (
                  <TableSortLabel
                    active={sortConfig.key === column.key}
                    direction={sortConfig.key === column.key ? sortConfig.direction : 'asc'}
                    onClick={() => handleSort(column.key)}
                    sx={{
                      color: 'white !important',
                      '& .MuiTableSortLabel-icon': {
                        color: 'white !important'
                      }
                    }}
                  >
                    {column.label}
                  </TableSortLabel>
                ) : (
                  column.label
                )}
              </TableCell>
            ))}
          </TableRow>
        </StyledTableHead>

        <TableBody>
          {sortedData.length > 0 ? (
            sortedData.map((row, index) => (
              <StyledTableRow
                key={row.id || index}
                onClick={() => onRowClick && onRowClick(row)}
              >
                {columns.map((column) => (
                  <StyledTableCell
                    key={column.key}
                    align={column.align || 'right'}
                  >
                    {getCellContent(row, column)}
                  </StyledTableCell>
                ))}
              </StyledTableRow>
            ))
          ) : (
            <TableRow>
              <StyledTableCell colSpan={columns.length} align="center">
                <Box sx={{ py: 4, textAlign: 'center' }}>
                  <Typography variant="body1" color="text.secondary">
                    {emptyMessage}
                  </Typography>
                </Box>
              </StyledTableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </StyledTableContainer>
  );
};

export default EnhancedTable;
