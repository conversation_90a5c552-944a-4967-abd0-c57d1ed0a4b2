import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  TextField,
  InputAdornment
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import BusinessIcon from '@mui/icons-material/Business';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import { useApp } from '../context/AppContext';
import FormDialog from '../components/FormDialog';
import ConfirmDialog from '../components/ConfirmDialog';
import SnackbarNotification from '../components/SnackbarNotification';

// نموذج إضافة/تعديل مورد
const SupplierForm = ({ open, onClose, supplier = null, onSave }) => {
  const [supplierData, setSupplierData] = useState({
    name: '',
    contact: '',
    phone: '',
    email: '',
    address: '',
    taxNumber: '',
    balance: 0,
    paymentTerms: 'نقدي',
    category: 'عام',
    notes: ''
  });

  React.useEffect(() => {
    if (supplier) {
      setSupplierData(supplier);
    } else {
      setSupplierData({
        name: '',
        contact: '',
        phone: '',
        email: '',
        address: '',
        taxNumber: '',
        balance: 0,
        paymentTerms: 'نقدي',
        category: 'عام',
        notes: ''
      });
    }
  }, [supplier]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setSupplierData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(supplierData);
    onClose();
  };

  return (
    <FormDialog
      open={open}
      onClose={onClose}
      onSubmit={handleSubmit}
      title={supplier ? 'تعديل المورد' : 'إضافة مورد جديد'}
      maxWidth="md"
    >
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6}>
          <TextField
            name="name"
            label="اسم المورد"
            value={supplierData.name}
            onChange={handleChange}
            fullWidth
            required
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            name="contact"
            label="جهة الاتصال"
            value={supplierData.contact}
            onChange={handleChange}
            fullWidth
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            name="phone"
            label="رقم الهاتف"
            value={supplierData.phone}
            onChange={handleChange}
            fullWidth
            required
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            name="email"
            label="البريد الإلكتروني"
            type="email"
            value={supplierData.email}
            onChange={handleChange}
            fullWidth
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            name="address"
            label="العنوان"
            value={supplierData.address}
            onChange={handleChange}
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            name="taxNumber"
            label="الرقم الضريبي"
            value={supplierData.taxNumber}
            onChange={handleChange}
            fullWidth
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            name="balance"
            label="الرصيد الافتتاحي"
            type="number"
            value={supplierData.balance}
            onChange={handleChange}
            fullWidth
            variant="outlined"
            margin="normal"
            InputProps={{
              startAdornment: <InputAdornment position="start">ر.س</InputAdornment>,
            }}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth margin="normal">
            <InputLabel>شروط الدفع</InputLabel>
            <Select
              name="paymentTerms"
              value={supplierData.paymentTerms}
              onChange={handleChange}
              label="شروط الدفع"
            >
              <MenuItem value="نقدي">نقدي</MenuItem>
              <MenuItem value="آجل 15 يوم">آجل 15 يوم</MenuItem>
              <MenuItem value="آجل 30 يوم">آجل 30 يوم</MenuItem>
              <MenuItem value="آجل 60 يوم">آجل 60 يوم</MenuItem>
              <MenuItem value="آجل 90 يوم">آجل 90 يوم</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth margin="normal">
            <InputLabel>فئة المورد</InputLabel>
            <Select
              name="category"
              value={supplierData.category}
              onChange={handleChange}
              label="فئة المورد"
            >
              <MenuItem value="عام">عام</MenuItem>
              <MenuItem value="تقنية">تقنية</MenuItem>
              <MenuItem value="مكتبية">مكتبية</MenuItem>
              <MenuItem value="خدمات">خدمات</MenuItem>
              <MenuItem value="مواد خام">مواد خام</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12}>
          <TextField
            name="notes"
            label="ملاحظات"
            value={supplierData.notes}
            onChange={handleChange}
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            margin="normal"
          />
        </Grid>
      </Grid>
    </FormDialog>
  );
};

function Suppliers() {
  const { state, addSupplier, updateSupplier, deleteSupplier } = useApp();
  const [searchQuery, setSearchQuery] = useState('');
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState({ open: false, supplierId: null });
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });

  // وظائف إدارة الموردين
  const handleSaveSupplier = (supplierData) => {
    if (editingSupplier) {
      updateSupplier({ ...supplierData, id: editingSupplier.id });
      setNotification({ open: true, message: 'تم تحديث المورد بنجاح', severity: 'success' });
    } else {
      addSupplier(supplierData);
      setNotification({ open: true, message: 'تم إضافة المورد بنجاح', severity: 'success' });
    }
    setEditingSupplier(null);
  };

  const handleEditSupplier = (supplier) => {
    setEditingSupplier(supplier);
    setOpenAddDialog(true);
  };

  const handleDeleteSupplier = (supplierId) => {
    setDeleteConfirm({ open: true, supplierId });
  };

  const confirmDelete = () => {
    deleteSupplier(deleteConfirm.supplierId);
    setDeleteConfirm({ open: false, supplierId: null });
    setNotification({ open: true, message: 'تم حذف المورد بنجاح', severity: 'success' });
  };

  const handleCloseDialog = () => {
    setOpenAddDialog(false);
    setEditingSupplier(null);
  };

  // حساب الإحصائيات
  const totalSuppliers = state.suppliers.length;
  const totalBalance = state.suppliers.reduce((sum, supplier) => sum + supplier.balance, 0);
  const activeSuppliers = state.suppliers.filter(supplier => supplier.balance > 0).length;
  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" sx={{ color: '#005B48', fontWeight: 'bold' }}>
          الموردين
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpenAddDialog(true)}
          sx={{
            backgroundColor: '#005B48',
            '&:hover': { backgroundColor: '#00483A' }
          }}
        >
          إضافة مورد جديد
        </Button>
      </Box>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#005B48', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <BusinessIcon fontSize="large" />
              <Box>
                <Typography variant="h6">إجمالي الموردين</Typography>
                <Typography variant="h4">{totalSuppliers}</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#00796B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <LocalShippingIcon fontSize="large" />
              <Box>
                <Typography variant="h6">الموردين النشطين</Typography>
                <Typography variant="h4">{activeSuppliers}</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#00897B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <AccountBalanceIcon fontSize="large" />
              <Box>
                <Typography variant="h6">إجمالي المستحقات</Typography>
                <Typography variant="h4">{totalBalance.toLocaleString()} ر.س</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="بحث عن مورد..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell>اسم الشركة</TableCell>
              <TableCell>الشخص المسؤول</TableCell>
              <TableCell>رقم الهاتف</TableCell>
              <TableCell>البريد الإلكتروني</TableCell>
              <TableCell>الرصيد</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {state.suppliers
              .filter(supplier =>
                supplier.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                supplier.contact.toLowerCase().includes(searchQuery.toLowerCase()) ||
                supplier.phone.includes(searchQuery) ||
                supplier.email.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .map((supplier) => (
              <TableRow key={supplier.id}>
                <TableCell>{supplier.name}</TableCell>
                <TableCell>{supplier.contact}</TableCell>
                <TableCell>{supplier.phone}</TableCell>
                <TableCell>{supplier.email}</TableCell>
                <TableCell>{supplier.balance?.toLocaleString()} ر.س</TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleEditSupplier(supplier)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteSupplier(supplier.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* نافذة إضافة/تعديل مورد */}
      <SupplierForm
        open={openAddDialog}
        onClose={handleCloseDialog}
        supplier={editingSupplier}
        onSave={handleSaveSupplier}
      />

      {/* حوار تأكيد الحذف */}
      <ConfirmDialog
        open={deleteConfirm.open}
        onClose={() => setDeleteConfirm({ open: false, supplierId: null })}
        onConfirm={confirmDelete}
        title="حذف المورد"
        message="هل أنت متأكد من أنك تريد حذف هذا المورد؟ لا يمكن التراجع عن هذا الإجراء."
      />

      {/* إشعارات */}
      <SnackbarNotification
        open={notification.open}
        onClose={() => setNotification({ ...notification, open: false })}
        message={notification.message}
        severity={notification.severity}
      />
    </Box>
  );
}

export default Suppliers;
