import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  TextField,
  InputAdornment
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import PersonIcon from '@mui/icons-material/Person';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import GroupIcon from '@mui/icons-material/Group';
import { useApp } from '../context/AppContext';
import FormDialog from '../components/FormDialog';
import ConfirmDialog from '../components/ConfirmDialog';
import SnackbarNotification from '../components/SnackbarNotification';

// نموذج إضافة/تعديل عميل
const CustomerForm = ({ open, onClose, customer = null, onSave }) => {
  const [customerData, setCustomerData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    balance: 0
  });

  React.useEffect(() => {
    if (customer) {
      setCustomerData(customer);
    } else {
      setCustomerData({
        name: '',
        phone: '',
        email: '',
        address: '',
        balance: 0
      });
    }
  }, [customer]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setCustomerData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(customerData);
    onClose();
  };

  return (
    <FormDialog
      open={open}
      onClose={onClose}
      onSubmit={handleSubmit}
      title={customer ? 'تعديل العميل' : 'إضافة عميل جديد'}
      maxWidth="sm"
    >
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <TextField
            name="name"
            label="اسم العميل"
            value={customerData.name}
            onChange={handleChange}
            fullWidth
            required
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            name="phone"
            label="رقم الهاتف"
            value={customerData.phone}
            onChange={handleChange}
            fullWidth
            required
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            name="email"
            label="البريد الإلكتروني"
            type="email"
            value={customerData.email}
            onChange={handleChange}
            fullWidth
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            name="address"
            label="العنوان"
            value={customerData.address}
            onChange={handleChange}
            fullWidth
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            name="balance"
            label="الرصيد الحالي"
            type="number"
            value={customerData.balance}
            onChange={handleChange}
            fullWidth
            variant="outlined"
            margin="normal"
            InputProps={{
              startAdornment: <InputAdornment position="start">ر.س</InputAdornment>,
            }}
          />
        </Grid>
      </Grid>
    </FormDialog>
  );
};

function Customers() {
  const { state, addCustomer, updateCustomer, deleteCustomer } = useApp();
  const [searchQuery, setSearchQuery] = useState('');
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState({ open: false, customerId: null });
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });

  // وظائف إدارة العملاء
  const handleSaveCustomer = (customerData) => {
    if (editingCustomer) {
      updateCustomer({ ...customerData, id: editingCustomer.id });
      setNotification({ open: true, message: 'تم تحديث العميل بنجاح', severity: 'success' });
    } else {
      addCustomer(customerData);
      setNotification({ open: true, message: 'تم إضافة العميل بنجاح', severity: 'success' });
    }
    setEditingCustomer(null);
  };

  const handleEditCustomer = (customer) => {
    setEditingCustomer(customer);
    setOpenAddDialog(true);
  };

  const handleDeleteCustomer = (customerId) => {
    setDeleteConfirm({ open: true, customerId });
  };

  const confirmDelete = () => {
    deleteCustomer(deleteConfirm.customerId);
    setDeleteConfirm({ open: false, customerId: null });
    setNotification({ open: true, message: 'تم حذف العميل بنجاح', severity: 'success' });
  };

  const handleCloseDialog = () => {
    setOpenAddDialog(false);
    setEditingCustomer(null);
  };

  // حساب الإحصائيات
  const totalCustomers = state.customers.length;
  const totalBalance = state.customers.reduce((sum, customer) => sum + customer.balance, 0);
  const activeCustomers = state.customers.filter(customer => customer.balance > 0).length;
  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" sx={{ color: '#005B48', fontWeight: 'bold' }}>
          العملاء
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpenAddDialog(true)}
          sx={{
            backgroundColor: '#005B48',
            '&:hover': { backgroundColor: '#00483A' }
          }}
        >
          إضافة عميل جديد
        </Button>
      </Box>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#005B48', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <GroupIcon fontSize="large" />
              <Box>
                <Typography variant="h6">إجمالي العملاء</Typography>
                <Typography variant="h4">{totalCustomers}</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#00796B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <PersonIcon fontSize="large" />
              <Box>
                <Typography variant="h6">العملاء النشطين</Typography>
                <Typography variant="h4">{activeCustomers}</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#00897B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <AttachMoneyIcon fontSize="large" />
              <Box>
                <Typography variant="h6">إجمالي الأرصدة</Typography>
                <Typography variant="h4">{totalBalance.toLocaleString()} ر.س</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="بحث عن عميل..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell>اسم العميل</TableCell>
              <TableCell>رقم الهاتف</TableCell>
              <TableCell>البريد الإلكتروني</TableCell>
              <TableCell>الرصيد</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {state.customers
              .filter(customer =>
                customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                customer.phone.includes(searchQuery) ||
                customer.email.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .map((customer) => (
              <TableRow key={customer.id}>
                <TableCell>{customer.name}</TableCell>
                <TableCell>{customer.phone}</TableCell>
                <TableCell>{customer.email}</TableCell>
                <TableCell>{customer.balance?.toLocaleString()} ر.س</TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleEditCustomer(customer)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteCustomer(customer.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* نافذة إضافة/تعديل عميل */}
      <CustomerForm
        open={openAddDialog}
        onClose={handleCloseDialog}
        customer={editingCustomer}
        onSave={handleSaveCustomer}
      />

      {/* حوار تأكيد الحذف */}
      <ConfirmDialog
        open={deleteConfirm.open}
        onClose={() => setDeleteConfirm({ open: false, customerId: null })}
        onConfirm={confirmDelete}
        title="حذف العميل"
        message="هل أنت متأكد من أنك تريد حذف هذا العميل؟ لا يمكن التراجع عن هذا الإجراء."
      />

      {/* إشعارات */}
      <SnackbarNotification
        open={notification.open}
        onClose={() => setNotification({ ...notification, open: false })}
        message={notification.message}
        severity={notification.severity}
      />
    </Box>
  );
}

export default Customers;
