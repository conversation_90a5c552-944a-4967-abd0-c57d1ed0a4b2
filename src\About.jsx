import React from 'react';
import { 
  Typography, 
  Container, 
  Box, 
  Grid, 
  Paper 
} from '@mui/material';
import CodeIcon from '@mui/icons-material/Code';
import GroupIcon from '@mui/icons-material/Group';
import TimelineIcon from '@mui/icons-material/Timeline';

const facts = [
  {
    icon: <CodeIcon sx={{ fontSize: 40, color: 'primary.main' }} />,
    number: "100+",
    text: "مشروع منجز"
  },
  {
    icon: <GroupIcon sx={{ fontSize: 40, color: 'primary.main' }} />,
    number: "50+",
    text: "عميل سعيد"
  },
  {
    icon: <TimelineIcon sx={{ fontSize: 40, color: 'primary.main' }} />,
    number: "5+",
    text: "سنوات خبرة"
  }
];

const About = () => (
  <Box 
    id="about" 
    sx={{ 
      py: 10,
      backgroundColor: 'white'
    }}
  >
    <Container maxWidth="lg">
      <Grid container spacing={8} alignItems="center">
        <Grid item xs={12} md={6}>
          <Typography 
            variant="h2" 
            sx={{ 
              fontWeight: 'bold',
              color: 'primary.main',
              mb: 4
            }}
          >
            عن موقع النور
          </Typography>
          <Typography 
            variant="body1" 
            sx={{ 
              fontSize: '1.1rem',
              color: 'text.secondary',
              mb: 3,
              lineHeight: 1.8
            }}
          >
            نحن فريق متخصص في تطوير المواقع والتطبيقات باستخدام أحدث التقنيات.
            نسعى دائماً لتقديم حلول مبتكرة تلبي احتياجات عملائنا وتتجاوز توقعاتهم.
          </Typography>
          <Typography 
            variant="body1" 
            sx={{ 
              fontSize: '1.1rem',
              color: 'text.secondary',
              lineHeight: 1.8
            }}
          >
            نؤمن بأن التكنولوجيا يجب أن تكون سهلة الاستخدام وفعالة في نفس الوقت،
            لذلك نحرص على تقديم تجربة مستخدم متميزة في جميع مشاريعنا.
          </Typography>
        </Grid>
        <Grid item xs={12} md={6}>
          <Grid container spacing={3}>
            {facts.map((fact, index) => (
              <Grid item xs={12} sm={4} key={index}>
                <Paper 
                  elevation={0}
                  sx={{ 
                    p: 3, 
                    textAlign: 'center',
                    backgroundColor: '#f8f9fa',
                    borderRadius: 4,
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'translateY(-8px)'
                    }
                  }}
                >
                  <Box sx={{ mb: 2 }}>
                    {fact.icon}
                  </Box>
                  <Typography 
                    variant="h3" 
                    sx={{ 
                      fontWeight: 'bold',
                      color: 'primary.main',
                      mb: 1
                    }}
                  >
                    {fact.number}
                  </Typography>
                  <Typography 
                    variant="body1"
                    color="text.secondary"
                  >
                    {fact.text}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </Grid>
      </Grid>
    </Container>
  </Box>
);

export default About;
