import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import { CssBaseline, Box, Container, Fab, Tooltip } from '@mui/material';
import { Menu as MenuIcon, Close as CloseIcon } from '@mui/icons-material';
import { AppProvider } from './context/AppContext';
import Sidebar from './components/Sidebar';
import ProtectedRoute from './components/ProtectedRoute';
import theme from './theme/theme';
import './App.css';

// استيراد الصفحات
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const Products = React.lazy(() => import('./pages/Products'));
const Warehouses = React.lazy(() => import('./pages/Warehouses'));
const Customers = React.lazy(() => import('./pages/Customers'));
const Suppliers = React.lazy(() => import('./pages/Suppliers'));
const Purchases = React.lazy(() => import('./pages/Purchases'));
const Sales = React.lazy(() => import('./pages/Sales'));
const Stores = React.lazy(() => import('./pages/Stores'));
const Invoices = React.lazy(() => import('./pages/Invoices'));
const Accounts = React.lazy(() => import('./pages/Accounts'));
const Finances = React.lazy(() => import('./pages/Finances'));
const Settings = React.lazy(() => import('./pages/Settings'));
const Reports = React.lazy(() => import('./pages/Reports'));
const CreateInvoice = React.lazy(() => import('./pages/CreateInvoice'));
const Login = React.lazy(() => import('./pages/Login'));

// مكون التحميل
const Loading = () => (
  <Box
    sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      color: '#005B48'
    }}
  >
    جاري التحميل...
  </Box>
);

function App() {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AppProvider>
        <Router>
          <Routes>
            {/* صفحة تسجيل الدخول */}
            <Route path="/login" element={
              <React.Suspense fallback={<Loading />}>
                <Login />
              </React.Suspense>
            } />

            {/* المسارات المحمية */}
            <Route path="/*" element={
              <ProtectedRoute>
                <Box sx={{
                  display: 'flex',
                  height: '100vh',
                  background: theme.palette.background.gradient,
                  position: 'relative'
                }}>
                  <Sidebar open={sidebarOpen} onClose={() => setSidebarOpen(false)} />

                  <Box
                    component="main"
                    sx={{
                      flexGrow: 1,
                      transition: 'margin 0.3s ease',
                      marginRight: sidebarOpen ? '280px' : '0',
                      position: 'relative',
                      overflow: 'auto'
                    }}
                  >
                    {/* زر فتح/إغلاق الشريط الجانبي */}
                    <Fab
                      color="primary"
                      size="medium"
                      onClick={toggleSidebar}
                      sx={{
                        position: 'fixed',
                        top: 20,
                        right: sidebarOpen ? 300 : 20,
                        zIndex: 1300,
                        transition: 'all 0.3s ease',
                        background: 'linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)',
                        boxShadow: '0 4px 20px rgba(76, 175, 80, 0.4)',
                        '&:hover': {
                          transform: 'scale(1.1)',
                          boxShadow: '0 6px 25px rgba(76, 175, 80, 0.6)'
                        }
                      }}
                    >
                      {sidebarOpen ? <CloseIcon /> : <MenuIcon />}
                    </Fab>

                    <Container
                      maxWidth={false}
                      sx={{
                        p: 3,
                        pt: 5,
                        '@media (min-width: 1200px)': {
                          px: 4
                        }
                      }}
                    >
                  <Routes>
              <Route path="/" element={<Navigate to="/dashboard" replace />} />
              <Route path="/dashboard" element={
                <React.Suspense fallback={<Loading />}>
                  <Dashboard />
                </React.Suspense>
              } />
              <Route path="/products" element={
                <React.Suspense fallback={<Loading />}>
                  <Products />
                </React.Suspense>
              } />
              <Route path="/warehouses" element={
                <React.Suspense fallback={<Loading />}>
                  <Warehouses />
                </React.Suspense>
              } />
              <Route path="/customers" element={
                <React.Suspense fallback={<Loading />}>
                  <Customers />
                </React.Suspense>
              } />
              <Route path="/suppliers" element={
                <React.Suspense fallback={<Loading />}>
                  <Suppliers />
                </React.Suspense>
              } />
              <Route path="/purchases" element={
                <React.Suspense fallback={<Loading />}>
                  <Purchases />
                </React.Suspense>
              } />
              <Route path="/sales" element={
                <React.Suspense fallback={<Loading />}>
                  <Sales />
                </React.Suspense>
              } />
              <Route path="/stores" element={
                <React.Suspense fallback={<Loading />}>
                  <Stores />
                </React.Suspense>
              } />
              <Route path="/invoices" element={
                <React.Suspense fallback={<Loading />}>
                  <Invoices />
                </React.Suspense>
              } />
              <Route path="/create-invoice" element={
                <React.Suspense fallback={<Loading />}>
                  <CreateInvoice />
                </React.Suspense>
              } />
              <Route path="/accounts" element={
                <React.Suspense fallback={<Loading />}>
                  <Accounts />
                </React.Suspense>
              } />
              <Route path="/finances" element={
                <React.Suspense fallback={<Loading />}>
                  <Finances />
                </React.Suspense>
              } />
              <Route path="/settings" element={
                <React.Suspense fallback={<Loading />}>
                  <Settings />
                </React.Suspense>
              } />
              <Route path="/reports" element={
                <React.Suspense fallback={<Loading />}>
                  <Reports />
                </React.Suspense>
              } />
                    </Routes>
                  </Container>
                </Box>
              </Box>
            </ProtectedRoute>
          } />
        </Routes>
      </Router>
    </AppProvider>
  </ThemeProvider>
  );
}

export default App;
