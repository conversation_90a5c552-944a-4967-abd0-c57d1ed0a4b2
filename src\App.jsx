import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Box, Container } from '@mui/material';
import Sidebar from './Sidebar';
import './App.css';

// استيراد الصفحات
const Products = React.lazy(() => import('./pages/Products'));
const Warehouses = React.lazy(() => import('./pages/Warehouses'));
const Customers = React.lazy(() => import('./pages/Customers'));
const Suppliers = React.lazy(() => import('./pages/Suppliers'));
const Purchases = React.lazy(() => import('./pages/Purchases'));
const Sales = React.lazy(() => import('./pages/Sales'));
const Stores = React.lazy(() => import('./pages/Stores'));
const Invoices = React.lazy(() => import('./pages/Invoices'));
const Accounts = React.lazy(() => import('./pages/Accounts'));
const Finances = React.lazy(() => import('./pages/Finances'));

// مكون التحميل
const Loading = () => (
  <Box 
    sx={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '100vh',
      color: '#005B48'
    }}
  >
    جاري التحميل...
  </Box>
);

function App() {
  return (
    <Router>
      <Box sx={{ display: 'flex', height: '100vh', backgroundColor: '#f5f7fa' }}>
        <Sidebar />
        <Box component="main" sx={{ flexGrow: 1, p: 0 }}>
          <Container 
            maxWidth={false} 
            sx={{ 
              p: 3,
              '@media (min-width: 1200px)': {
                px: 4
              }
            }}
          >
            <Routes>
              <Route path="/" element={<Navigate to="/products" replace />} />
              <Route path="/products" element={
                <React.Suspense fallback={<Loading />}>
                  <Products />
                </React.Suspense>
              } />
              <Route path="/warehouses" element={
                <React.Suspense fallback={<Loading />}>
                  <Warehouses />
                </React.Suspense>
              } />
              <Route path="/customers" element={
                <React.Suspense fallback={<Loading />}>
                  <Customers />
                </React.Suspense>
              } />
              <Route path="/suppliers" element={
                <React.Suspense fallback={<Loading />}>
                  <Suppliers />
                </React.Suspense>
              } />
              <Route path="/purchases" element={
                <React.Suspense fallback={<Loading />}>
                  <Purchases />
                </React.Suspense>
              } />
              <Route path="/sales" element={
                <React.Suspense fallback={<Loading />}>
                  <Sales />
                </React.Suspense>
              } />
              <Route path="/stores" element={
                <React.Suspense fallback={<Loading />}>
                  <Stores />
                </React.Suspense>
              } />
              <Route path="/invoices" element={
                <React.Suspense fallback={<Loading />}>
                  <Invoices />
                </React.Suspense>
              } />
              <Route path="/accounts" element={
                <React.Suspense fallback={<Loading />}>
                  <Accounts />
                </React.Suspense>
              } />
              <Route path="/finances" element={
                <React.Suspense fallback={<Loading />}>
                  <Finances />
                </React.Suspense>
              } />
            </Routes>
          </Container>
        </Box>
      </Box>
    </Router>
  );
}

export default App;
