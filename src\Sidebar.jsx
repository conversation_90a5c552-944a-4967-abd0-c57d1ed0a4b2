import React from 'react';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Avatar
} from '@mui/material';
import { useLocation, useNavigate } from 'react-router-dom';
import InventoryIcon from '@mui/icons-material/Inventory';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import DescriptionIcon from '@mui/icons-material/Description';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import MonetizationOnIcon from '@mui/icons-material/MonetizationOn';
import LocalShippingIcon from '@mui/icons-material/LocalShipping';
import GroupIcon from '@mui/icons-material/Group';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import PaidIcon from '@mui/icons-material/Paid';
import StoreIcon from '@mui/icons-material/Store';
import DashboardIcon from '@mui/icons-material/Dashboard';
import SettingsIcon from '@mui/icons-material/Settings';

const menuItems = [
  { label: 'لوحة التحكم', icon: <DashboardIcon />, path: '/dashboard' },
  { label: 'المنتجات', icon: <InventoryIcon />, path: '/products' },
  { label: 'المخازن', icon: <WarehouseIcon />, path: '/warehouses' },
  { label: 'الفواتير', icon: <DescriptionIcon />, path: '/invoices' },
  { label: 'الحسابات', icon: <AccountBalanceIcon />, path: '/accounts' },
  { label: 'الماليات', icon: <MonetizationOnIcon />, path: '/finances' },
  { label: 'الموردين', icon: <LocalShippingIcon />, path: '/suppliers' },
  { label: 'العملاء', icon: <GroupIcon />, path: '/customers' },
  { label: 'المشتريات', icon: <ShoppingCartIcon />, path: '/purchases' },
  { label: 'المبيعات', icon: <PaidIcon />, path: '/sales' },
  { label: 'المتاجر', icon: <StoreIcon />, path: '/stores' },
  { label: 'الإعدادات', icon: <SettingsIcon />, path: '/settings' }
];

const Sidebar = () => {
  const location = useLocation();
  const navigate = useNavigate();

  return (
    <Drawer
      variant="permanent"
      anchor="right"
      sx={{
        width: 240,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: 240,
          boxSizing: 'border-box',
          backgroundColor: '#005B48',
          color: 'white',
          borderLeft: 'none',
        },
      }}
    >
      <Box
        sx={{
          p: 2,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
        }}
      >
        <Avatar
          sx={{
            width: 40,
            height: 40,
            bgcolor: 'rgba(255, 255, 255, 0.2)',
            color: 'white',
            fontSize: '1.2rem',
            fontWeight: 'bold',
            mb: 1
          }}
        >
          H
        </Avatar>
        <Typography variant="subtitle1" sx={{ fontWeight: 'medium' }}>
          Hazem
        </Typography>
      </Box>

      <List sx={{ p: 2 }}>
        {menuItems.map((item) => (
          <ListItem key={item.path} disablePadding sx={{ mb: 1 }}>
            <ListItemButton
              selected={location.pathname === item.path}
              onClick={() => navigate(item.path)}
              sx={{
                borderRadius: 2,
                '&.Mui-selected': {
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                  '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 0.15)',
                  },
                },
                '&:hover': {
                  backgroundColor: 'rgba(255, 255, 255, 0.08)',
                },
              }}
            >
              <ListItemIcon
                sx={{
                  minWidth: 40,
                  color: 'white',
                  opacity: location.pathname === item.path ? 1 : 0.7
                }}
              >
                {item.icon}
              </ListItemIcon>
              <ListItemText
                primary={item.label}
                sx={{
                  '& .MuiTypography-root': {
                    fontSize: '0.9rem',
                    fontWeight: location.pathname === item.path ? 'bold' : 'normal',
                    opacity: location.pathname === item.path ? 1 : 0.7
                  }
                }}
              />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Drawer>
  );
};

export default Sidebar;
