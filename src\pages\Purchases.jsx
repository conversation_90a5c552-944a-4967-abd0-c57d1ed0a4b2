import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  TextField,
  InputAdornment
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';
import ReceiptIcon from '@mui/icons-material/Receipt';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';

// بيانات تجريبية للمشتريات
const purchasesData = [
  { 
    id: 1, 
    date: '2025-05-25',
    supplier: 'شركة الأمل للتوريدات',
    invoice: 'INV-001',
    total: 25000,
    status: 'مكتمل'
  },
  { 
    id: 2, 
    date: '2025-05-24',
    supplier: 'مؤسسة النور التجارية',
    invoice: 'INV-002',
    total: 18500,
    status: 'قيد التنفيذ'
  },
  { 
    id: 3, 
    date: '2025-05-23',
    supplier: 'شركة السلام للتجارة',
    invoice: 'INV-003',
    total: 32000,
    status: 'مكتمل'
  },
];

function Purchases() {
  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" sx={{ color: '#005B48', fontWeight: 'bold' }}>
          المشتريات
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          sx={{
            backgroundColor: '#005B48',
            '&:hover': { backgroundColor: '#00483A' }
          }}
        >
          إضافة أمر شراء
        </Button>
      </Box>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#005B48', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <ShoppingCartIcon fontSize="large" />
              <Box>
                <Typography variant="h6">إجمالي المشتريات</Typography>
                <Typography variant="h4">75,500</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#00796B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <ReceiptIcon fontSize="large" />
              <Box>
                <Typography variant="h6">عدد الفواتير</Typography>
                <Typography variant="h4">{purchasesData.length}</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#00897B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <TrendingUpIcon fontSize="large" />
              <Box>
                <Typography variant="h6">معدل الشراء اليومي</Typography>
                <Typography variant="h4">25,167</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="بحث في المشتريات..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell>التاريخ</TableCell>
              <TableCell>المورد</TableCell>
              <TableCell>رقم الفاتورة</TableCell>
              <TableCell>الإجمالي</TableCell>
              <TableCell>الحالة</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {purchasesData.map((purchase) => (
              <TableRow key={purchase.id}>
                <TableCell>{purchase.date}</TableCell>
                <TableCell>{purchase.supplier}</TableCell>
                <TableCell>{purchase.invoice}</TableCell>
                <TableCell>{purchase.total} ج.م</TableCell>
                <TableCell>
                  <Typography
                    sx={{
                      color: purchase.status === 'مكتمل' ? 'success.main' : 'warning.main',
                      fontWeight: 'medium'
                    }}
                  >
                    {purchase.status}
                  </Typography>
                </TableCell>
                <TableCell>
                  <IconButton size="small" color="primary">
                    <EditIcon />
                  </IconButton>
                  <IconButton size="small" color="error">
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}

export default Purchases;
