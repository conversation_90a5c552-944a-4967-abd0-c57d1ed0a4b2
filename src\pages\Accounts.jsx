import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  InputAdornment,
  IconButton,
  Button,
  Chip,
  Card,
  CardContent,
  Grid
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';

// بيانات تجريبية للحسابات
const accountsData = [
  {
    id: 1,
    name: 'الصندوق الرئيسي',
    type: 'صندوق',
    balance: 50000,
    lastTransaction: '2025-05-26',
    status: 'نشط'
  },
  {
    id: 2,
    name: 'البنك الأهلي',
    type: 'بنك',
    balance: 150000,
    lastTransaction: '2025-05-25',
    status: 'نشط'
  },
  {
    id: 3,
    name: 'صندوق المصروفات',
    type: 'صندوق',
    balance: 15000,
    lastTransaction: '2025-05-24',
    status: 'نشط'
  }
];

function Accounts() {
  const [searchQuery, setSearchQuery] = useState('');

  // حساب الإجماليات
  const totalBalance = accountsData.reduce((sum, account) => sum + account.balance, 0);
  const cashBalance = accountsData
    .filter(account => account.type === 'صندوق')
    .reduce((sum, account) => sum + account.balance, 0);
  const bankBalance = accountsData
    .filter(account => account.type === 'بنك')
    .reduce((sum, account) => sum + account.balance, 0);

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" sx={{ color: '#005B48', fontWeight: 'bold' }}>
          الحسابات
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          sx={{
            backgroundColor: '#005B48',
            '&:hover': { backgroundColor: '#00483A' }
          }}
        >
          إضافة حساب جديد
        </Button>
      </Box>

      {/* البطاقات الإحصائية */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} lg={3}>
          <Card sx={{ bgcolor: '#005B48', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <AccountBalanceIcon fontSize="large" />
              <Box>
                <Typography variant="subtitle2">إجمالي الأرصدة</Typography>
                <Typography variant="h5">{totalBalance.toLocaleString()} ر.س</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <Card sx={{ bgcolor: '#00796B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <AccountBalanceWalletIcon fontSize="large" />
              <Box>
                <Typography variant="subtitle2">رصيد الصناديق</Typography>
                <Typography variant="h5">{cashBalance.toLocaleString()} ر.س</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <Card sx={{ bgcolor: '#00897B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <TrendingUpIcon fontSize="large" />
              <Box>
                <Typography variant="subtitle2">رصيد البنوك</Typography>
                <Typography variant="h5">{bankBalance.toLocaleString()} ر.س</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <Card sx={{ bgcolor: '#26A69A', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <TrendingDownIcon fontSize="large" />
              <Box>
                <Typography variant="subtitle2">عدد الحسابات</Typography>
                <Typography variant="h5">{accountsData.length}</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* شريط البحث */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="بحث في الحسابات..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              bgcolor: '#f5f7fa',
              '&:hover fieldset': {
                borderColor: '#005B48',
              },
            },
          }}
        />
      </Box>

      {/* جدول الحسابات */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell>اسم الحساب</TableCell>
              <TableCell>نوع الحساب</TableCell>
              <TableCell>الرصيد الحالي</TableCell>
              <TableCell>آخر حركة</TableCell>
              <TableCell>الحالة</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {accountsData
              .filter(account => 
                account.name.includes(searchQuery) || 
                account.type.includes(searchQuery)
              )
              .map((account) => (
                <TableRow key={account.id}>
                  <TableCell>{account.name}</TableCell>
                  <TableCell>
                    <Chip 
                      label={account.type}
                      size="small"
                      sx={{ 
                        bgcolor: account.type === 'صندوق' ? '#e3f2fd' : '#f3e5f5',
                        color: account.type === 'صندوق' ? '#1976d2' : '#9c27b0',
                      }}
                    />
                  </TableCell>
                  <TableCell>{account.balance.toLocaleString()} ر.س</TableCell>
                  <TableCell>{account.lastTransaction}</TableCell>
                  <TableCell>
                    <Chip
                      label={account.status}
                      color={account.status === 'نشط' ? 'success' : 'warning'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <IconButton size="small" color="primary">
                        <EditIcon />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}

export default Accounts;
