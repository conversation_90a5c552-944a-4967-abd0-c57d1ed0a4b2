import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tabs,
  Tab
} from '@mui/material';
import {
  Print as PrintIcon,
  GetApp as DownloadIcon,
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  Inventory as InventoryIcon,
  Group as GroupIcon,
  AttachMoney as AttachMoneyIcon,
  DateRange as DateRangeIcon
} from '@mui/icons-material';
import { useApp } from '../context/AppContext';
import { exportToCSV, exportToJSON, printReport } from '../utils/exportUtils';
import SnackbarNotification from '../components/SnackbarNotification';

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`reports-tabpanel-${index}`}
    aria-labelledby={`reports-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
  </div>
);

const ReportCard = ({ title, description, icon, onGenerate, onPrint, onExport }) => (
  <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
    <CardContent sx={{ flexGrow: 1 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Box
          sx={{
            backgroundColor: '#005B4815',
            borderRadius: 2,
            p: 1,
            mr: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          {React.cloneElement(icon, { sx: { color: '#005B48', fontSize: 24 } })}
        </Box>
        <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
          {title}
        </Typography>
      </Box>
      <Typography variant="body2" color="text.secondary">
        {description}
      </Typography>
    </CardContent>
    <CardActions sx={{ p: 2, pt: 0 }}>
      <Button
        size="small"
        onClick={onGenerate}
        sx={{ color: '#005B48' }}
      >
        عرض
      </Button>
      <Button
        size="small"
        startIcon={<PrintIcon />}
        onClick={onPrint}
        sx={{ color: '#005B48' }}
      >
        طباعة
      </Button>
      <Button
        size="small"
        startIcon={<DownloadIcon />}
        onClick={onExport}
        sx={{ color: '#005B48' }}
      >
        تصدير
      </Button>
    </CardActions>
  </Card>
);

const Reports = () => {
  const { state } = useApp();
  const [currentTab, setCurrentTab] = useState(0);
  const [dateRange, setDateRange] = useState({
    from: new Date().toISOString().split('T')[0],
    to: new Date().toISOString().split('T')[0]
  });
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const showNotification = (message, severity = 'success') => {
    setNotification({ open: true, message, severity });
  };

  // تقارير المنتجات
  const generateProductsReport = () => {
    return state.products.map(product => ({
      'كود المنتج': product.code,
      'اسم المنتج': product.name,
      'الفئة': product.category,
      'الماركة': product.brand,
      'سعر البيع': product.salePrice,
      'سعر الشراء': product.purchasePrice,
      'الكمية': product.quantity,
      'الوحدة': product.unit,
      'الحد الأدنى': product.minQuantity
    }));
  };

  const handleProductsExport = () => {
    const data = generateProductsReport();
    exportToCSV(data, 'تقرير_المنتجات');
    showNotification('تم تصدير تقرير المنتجات بنجاح');
  };

  const handleProductsPrint = () => {
    const data = generateProductsReport();
    const columns = [
      { key: 'كود المنتج', label: 'كود المنتج' },
      { key: 'اسم المنتج', label: 'اسم المنتج' },
      { key: 'الفئة', label: 'الفئة' },
      { key: 'سعر البيع', label: 'سعر البيع' },
      { key: 'الكمية', label: 'الكمية' }
    ];
    printReport('تقرير المنتجات', data, columns);
  };

  // تقارير العملاء
  const generateCustomersReport = () => {
    return state.customers.map(customer => ({
      'اسم العميل': customer.name,
      'رقم الهاتف': customer.phone,
      'البريد الإلكتروني': customer.email,
      'العنوان': customer.address,
      'الرصيد': customer.balance
    }));
  };

  const handleCustomersExport = () => {
    const data = generateCustomersReport();
    exportToCSV(data, 'تقرير_العملاء');
    showNotification('تم تصدير تقرير العملاء بنجاح');
  };

  const handleCustomersPrint = () => {
    const data = generateCustomersReport();
    const columns = [
      { key: 'اسم العميل', label: 'اسم العميل' },
      { key: 'رقم الهاتف', label: 'رقم الهاتف' },
      { key: 'البريد الإلكتروني', label: 'البريد الإلكتروني' },
      { key: 'الرصيد', label: 'الرصيد' }
    ];
    printReport('تقرير العملاء', data, columns);
  };

  // تقارير الموردين
  const generateSuppliersReport = () => {
    return state.suppliers.map(supplier => ({
      'اسم المورد': supplier.name,
      'جهة الاتصال': supplier.contact,
      'رقم الهاتف': supplier.phone,
      'البريد الإلكتروني': supplier.email,
      'العنوان': supplier.address,
      'الرصيد': supplier.balance
    }));
  };

  const handleSuppliersExport = () => {
    const data = generateSuppliersReport();
    exportToCSV(data, 'تقرير_الموردين');
    showNotification('تم تصدير تقرير الموردين بنجاح');
  };

  const handleSuppliersPrint = () => {
    const data = generateSuppliersReport();
    const columns = [
      { key: 'اسم المورد', label: 'اسم المورد' },
      { key: 'جهة الاتصال', label: 'جهة الاتصال' },
      { key: 'رقم الهاتف', label: 'رقم الهاتف' },
      { key: 'الرصيد', label: 'الرصيد' }
    ];
    printReport('تقرير الموردين', data, columns);
  };

  // تقارير المخازن
  const generateWarehousesReport = () => {
    return state.warehouses.map(warehouse => ({
      'اسم المخزن': warehouse.name,
      'الموقع': warehouse.location,
      'السعة الإجمالية': warehouse.capacity,
      'المساحة المستخدمة': warehouse.used,
      'نسبة الاستخدام': warehouse.capacity > 0 ? `${Math.round((warehouse.used / warehouse.capacity) * 100)}%` : '0%',
      'الحالة': warehouse.status,
      'المدير': warehouse.manager
    }));
  };

  const handleWarehousesExport = () => {
    const data = generateWarehousesReport();
    exportToCSV(data, 'تقرير_المخازن');
    showNotification('تم تصدير تقرير المخازن بنجاح');
  };

  const handleWarehousesPrint = () => {
    const data = generateWarehousesReport();
    const columns = [
      { key: 'اسم المخزن', label: 'اسم المخزن' },
      { key: 'الموقع', label: 'الموقع' },
      { key: 'السعة الإجمالية', label: 'السعة الإجمالية' },
      { key: 'المساحة المستخدمة', label: 'المساحة المستخدمة' },
      { key: 'نسبة الاستخدام', label: 'نسبة الاستخدام' },
      { key: 'الحالة', label: 'الحالة' }
    ];
    printReport('تقرير المخازن', data, columns);
  };

  // حساب الإحصائيات
  const totalProducts = state.products.length;
  const totalCustomers = state.customers.length;
  const totalSuppliers = state.suppliers.length;
  const totalWarehouses = state.warehouses.length;
  const lowStockProducts = state.products.filter(p => p.quantity <= p.minQuantity).length;
  const totalSales = state.sales.reduce((sum, sale) => sum + sale.total, 0);
  const totalPurchases = state.purchases.reduce((sum, purchase) => sum + purchase.total, 0);

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#005B48', mb: 1 }}>
          التقارير والإحصائيات
        </Typography>
        <Typography variant="body1" color="textSecondary">
          عرض وتصدير التقارير المختلفة للنظام
        </Typography>
      </Box>

      {/* الإحصائيات السريعة */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#005B48', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {totalProducts}
                  </Typography>
                  <Typography variant="body2">
                    إجمالي المنتجات
                  </Typography>
                </Box>
                <InventoryIcon sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#1976d2', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {totalCustomers}
                  </Typography>
                  <Typography variant="body2">
                    إجمالي العملاء
                  </Typography>
                </Box>
                <GroupIcon sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#2e7d32', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {totalSales.toLocaleString()}
                  </Typography>
                  <Typography variant="body2">
                    إجمالي المبيعات (ر.س)
                  </Typography>
                </Box>
                <AttachMoneyIcon sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card sx={{ bgcolor: '#ed6c02', color: 'white' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Box>
                  <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
                    {lowStockProducts}
                  </Typography>
                  <Typography variant="body2">
                    منتجات منخفضة المخزون
                  </Typography>
                </Box>
                <TrendingUpIcon sx={{ fontSize: 40, opacity: 0.8 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* التبويبات */}
      <Paper sx={{ borderRadius: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={currentTab} onChange={handleTabChange}>
            <Tab label="تقارير المنتجات" />
            <Tab label="تقارير العملاء" />
            <Tab label="تقارير الموردين" />
            <Tab label="تقارير المخازن" />
          </Tabs>
        </Box>

        {/* تقارير المنتجات */}
        <TabPanel value={currentTab} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <ReportCard
                title="تقرير جميع المنتجات"
                description="قائمة شاملة بجميع المنتجات مع تفاصيلها الكاملة"
                icon={<InventoryIcon />}
                onGenerate={() => showNotification('تم إنشاء التقرير')}
                onPrint={handleProductsPrint}
                onExport={handleProductsExport}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <ReportCard
                title="تقرير المنتجات منخفضة المخزون"
                description="المنتجات التي وصلت إلى الحد الأدنى للمخزون"
                icon={<TrendingUpIcon />}
                onGenerate={() => showNotification('تم إنشاء التقرير')}
                onPrint={() => {
                  const lowStock = state.products.filter(p => p.quantity <= p.minQuantity);
                  const data = lowStock.map(product => ({
                    'اسم المنتج': product.name,
                    'الكمية الحالية': product.quantity,
                    'الحد الأدنى': product.minQuantity,
                    'الوحدة': product.unit
                  }));
                  const columns = [
                    { key: 'اسم المنتج', label: 'اسم المنتج' },
                    { key: 'الكمية الحالية', label: 'الكمية الحالية' },
                    { key: 'الحد الأدنى', label: 'الحد الأدنى' },
                    { key: 'الوحدة', label: 'الوحدة' }
                  ];
                  printReport('تقرير المنتجات منخفضة المخزون', data, columns);
                }}
                onExport={() => {
                  const lowStock = state.products.filter(p => p.quantity <= p.minQuantity);
                  const data = lowStock.map(product => ({
                    'اسم المنتج': product.name,
                    'الكمية الحالية': product.quantity,
                    'الحد الأدنى': product.minQuantity,
                    'الوحدة': product.unit
                  }));
                  exportToCSV(data, 'المنتجات_منخفضة_المخزون');
                  showNotification('تم تصدير التقرير بنجاح');
                }}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* تقارير العملاء */}
        <TabPanel value={currentTab} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <ReportCard
                title="تقرير جميع العملاء"
                description="قائمة شاملة بجميع العملاء مع بياناتهم الكاملة"
                icon={<GroupIcon />}
                onGenerate={() => showNotification('تم إنشاء التقرير')}
                onPrint={handleCustomersPrint}
                onExport={handleCustomersExport}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* تقارير الموردين */}
        <TabPanel value={currentTab} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <ReportCard
                title="تقرير جميع الموردين"
                description="قائمة شاملة بجميع الموردين مع بياناتهم الكاملة"
                icon={<GroupIcon />}
                onGenerate={() => showNotification('تم إنشاء التقرير')}
                onPrint={handleSuppliersPrint}
                onExport={handleSuppliersExport}
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* تقارير المخازن */}
        <TabPanel value={currentTab} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <ReportCard
                title="تقرير جميع المخازن"
                description="قائمة شاملة بجميع المخازن مع تفاصيل الاستخدام"
                icon={<AssessmentIcon />}
                onGenerate={() => showNotification('تم إنشاء التقرير')}
                onPrint={handleWarehousesPrint}
                onExport={handleWarehousesExport}
              />
            </Grid>
          </Grid>
        </TabPanel>
      </Paper>

      {/* إشعارات */}
      <SnackbarNotification
        open={notification.open}
        onClose={() => setNotification({ ...notification, open: false })}
        message={notification.message}
        severity={notification.severity}
      />
    </Box>
  );
};

export default Reports;
