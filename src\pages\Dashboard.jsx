import React from 'react';
import {
  Box,
  Grid,
  Typography,
  Paper,
  LinearProgress,
  Chip
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Inventory as InventoryIcon,
  Group as GroupIcon,
  AttachMoney as AttachMoneyIcon,
  ShoppingCart as ShoppingCartIcon,
  Store as StoreIcon,
  AccountBalance as AccountBalanceIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useApp } from '../context/AppContext';
import GradientCard from '../components/ui/GradientCard';
import AnimatedButton from '../components/ui/AnimatedButton';



const ProgressCard = ({ title, current, total, color }) => {
  const percentage = (current / total) * 100;

  return (
    <Paper sx={{ p: 3, borderRadius: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
        {title}
      </Typography>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
        <Typography variant="body2" color="textSecondary">
          {current.toLocaleString()} من {total.toLocaleString()}
        </Typography>
        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
          {percentage.toFixed(1)}%
        </Typography>
      </Box>
      <LinearProgress
        variant="determinate"
        value={percentage}
        sx={{
          height: 8,
          borderRadius: 4,
          backgroundColor: `${color}20`,
          '& .MuiLinearProgress-bar': {
            backgroundColor: color,
            borderRadius: 4,
          },
        }}
      />
    </Paper>
  );
};

const RecentActivity = ({ activities }) => (
  <Paper sx={{ p: 3, borderRadius: 3, height: '100%' }}>
    <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
      النشاط الأخير
    </Typography>
    <Box sx={{ mt: 2 }}>
      {activities.map((activity, index) => (
        <Box
          key={index}
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            py: 2,
            borderBottom: index < activities.length - 1 ? '1px solid #f0f0f0' : 'none'
          }}
        >
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {activity.action}
            </Typography>
            <Typography variant="caption" color="textSecondary">
              {activity.time}
            </Typography>
          </Box>
          <Chip
            label={activity.type}
            size="small"
            sx={{
              backgroundColor: activity.color + '20',
              color: activity.color,
              fontWeight: 'medium'
            }}
          />
        </Box>
      ))}
    </Box>
  </Paper>
);

const Dashboard = () => {
  const { state } = useApp();

  // حساب الإحصائيات
  const totalProducts = state.products.length;
  const totalCustomers = state.customers.length;
  const totalSuppliers = state.suppliers.length;
  const totalSales = state.sales.reduce((sum, sale) => sum + sale.total, 0);
  const totalPurchases = state.purchases.reduce((sum, purchase) => sum + purchase.total, 0);
  const totalBalance = state.accounts.reduce((sum, account) => sum + account.balance, 0);

  // المنتجات منخفضة المخزون
  const lowStockProducts = state.products.filter(product =>
    product.quantity <= product.minQuantity
  );

  // النشاط الأخير (بيانات ديناميكية)
  const recentActivities = [
    { action: `تم إضافة ${state.products.length > 0 ? state.products[state.products.length - 1]?.name || 'منتج جديد' : 'منتج جديد'}`, time: 'منذ 5 دقائق', type: 'منتج', color: '#005B48' },
    { action: `تم إنشاء فاتورة رقم ${state.invoices.length + 1}`, time: 'منذ 15 دقيقة', type: 'فاتورة', color: '#1976d2' },
    { action: `تم تحديث بيانات ${state.customers.length > 0 ? state.customers[0]?.name || 'عميل' : 'عميل'}`, time: 'منذ 30 دقيقة', type: 'عميل', color: '#ed6c02' },
    { action: `تم تسجيل مبيعات بقيمة ${totalSales.toLocaleString()} ر.س`, time: 'منذ ساعة', type: 'مبيعات', color: '#2e7d32' },
    { action: `تم إضافة ${state.suppliers.length > 0 ? state.suppliers[state.suppliers.length - 1]?.name || 'مورد جديد' : 'مورد جديد'}`, time: 'منذ ساعتين', type: 'مورد', color: '#9c27b0' },
  ];

  return (
    <Box sx={{
      p: 3,
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      minHeight: '100vh'
    }}>
      {/* رأس الصفحة المحسن */}
      <Paper
        elevation={4}
        sx={{
          p: 4,
          mb: 4,
          borderRadius: 4,
          background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
          border: '2px solid rgba(0,91,72,0.1)',
          boxShadow: '0 12px 40px rgba(0,91,72,0.15)'
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
            <Box
              sx={{
                p: 2,
                borderRadius: 4,
                background: 'linear-gradient(135deg, #005B48 0%, #00796B 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 6px 20px rgba(0,91,72,0.3)'
              }}
            >
              <AccountBalanceIcon sx={{ color: 'white', fontSize: 32 }} />
            </Box>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#005B48', mb: 1 }}>
                🏢 لوحة التحكم
              </Typography>
              <Typography variant="h6" color="textSecondary" sx={{ fontWeight: 'medium' }}>
                مرحباً بك في نظام إدارة الأعمال - النور
              </Typography>
            </Box>
          </Box>
          <Box sx={{ textAlign: 'right' }}>
            <Box sx={{
              p: 2,
              borderRadius: 3,
              bgcolor: 'rgba(0,91,72,0.05)',
              border: '1px solid rgba(0,91,72,0.1)'
            }}>
              <Typography variant="body1" sx={{ fontWeight: 'bold', color: '#005B48', mb: 0.5 }}>
                📅 {new Date().toLocaleDateString('ar-SA')}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                🕐 {new Date().toLocaleTimeString('ar-SA')}
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* ملخص سريع محسن */}
        <Box sx={{
          p: 3,
          borderRadius: 3,
          background: 'linear-gradient(135deg, #005B48 0%, #00796B 100%)',
          color: 'white',
          boxShadow: '0 8px 32px rgba(0,91,72,0.3)'
        }}>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold', textAlign: 'center' }}>
            📊 الملخص المالي السريع
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} lg={3}>
              <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, bgcolor: 'rgba(255,255,255,0.1)' }}>
                <Typography variant="body2" sx={{ opacity: 0.9, mb: 1 }}>
                  💰 إجمالي الأصول
                </Typography>
                <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                  {(totalSales + totalBalance).toLocaleString()} ر.س
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={3}>
              <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, bgcolor: 'rgba(255,255,255,0.1)' }}>
                <Typography variant="body2" sx={{ opacity: 0.9, mb: 1 }}>
                  📈 صافي الربح
                </Typography>
                <Typography variant="h5" sx={{
                  fontWeight: 'bold',
                  color: totalSales > totalPurchases ? '#4caf50' : '#f44336'
                }}>
                  {(totalSales - totalPurchases).toLocaleString()} ر.س
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={3}>
              <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, bgcolor: 'rgba(255,255,255,0.1)' }}>
                <Typography variant="body2" sx={{ opacity: 0.9, mb: 1 }}>
                  📊 معدل النمو
                </Typography>
                <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                  +15.2%
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={3}>
              <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, bgcolor: 'rgba(255,255,255,0.1)' }}>
                <Typography variant="body2" sx={{ opacity: 0.9, mb: 1 }}>
                  ⚡ حالة النظام
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                  <Box sx={{ width: 10, height: 10, borderRadius: '50%', bgcolor: '#4caf50' }} />
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                    نشط
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Paper>

      {/* البطاقات الإحصائية */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} lg={3}>
          <GradientCard
            title="📦 إجمالي المنتجات"
            subtitle="عدد المنتجات المسجلة في النظام"
            value={totalProducts.toLocaleString()}
            icon={<InventoryIcon />}
            iconColor="#4CAF50"
            gradient="linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%)"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <GradientCard
            title="👥 إجمالي العملاء"
            subtitle="عدد العملاء المسجلين"
            value={totalCustomers.toLocaleString()}
            icon={<GroupIcon />}
            iconColor="#2196F3"
            gradient="linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%)"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <GradientCard
            title="💰 إجمالي المبيعات"
            subtitle="إجمالي قيمة المبيعات"
            value={`${totalSales.toLocaleString()} ر.س`}
            icon={<AttachMoneyIcon />}
            iconColor="#FF9800"
            gradient="linear-gradient(135deg, #FFF3E0 0%, #FFE0B2 100%)"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <GradientCard
            title="🏦 الرصيد الإجمالي"
            subtitle="إجمالي الأرصدة المتاحة"
            value={`${totalBalance.toLocaleString()} ر.س`}
            icon={<AccountBalanceIcon />}
            iconColor="#9C27B0"
            gradient="linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%)"
          />
        </Grid>
      </Grid>

      {/* الصف الثاني من الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} lg={3}>
          <GradientCard
            title="🏢 إجمالي الموردين"
            subtitle="عدد الموردين المسجلين"
            value={totalSuppliers.toLocaleString()}
            icon={<StoreIcon />}
            iconColor="#9C27B0"
            gradient="linear-gradient(135deg, #F3E5F5 0%, #E1BEE7 100%)"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <GradientCard
            title="🛒 إجمالي المشتريات"
            subtitle="إجمالي قيمة المشتريات"
            value={`${totalPurchases.toLocaleString()} ر.س`}
            icon={<ShoppingCartIcon />}
            iconColor="#FF5722"
            gradient="linear-gradient(135deg, #FBE9E7 0%, #FFCCBC 100%)"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <GradientCard
            title="⚠️ تنبيهات المخزون"
            subtitle="منتجات منخفضة المخزون"
            value={lowStockProducts.length.toLocaleString()}
            icon={<WarningIcon />}
            iconColor="#F44336"
            gradient="linear-gradient(135deg, #FFEBEE 0%, #FFCDD2 100%)"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <GradientCard
            title="📈 صافي الربح"
            subtitle="الربح الإجمالي المحقق"
            value={`${(totalSales - totalPurchases).toLocaleString()} ر.س`}
            icon={<TrendingUpIcon />}
            iconColor="#4CAF50"
            gradient="linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%)"
          />
        </Grid>
      </Grid>

      {/* الصف الثالث من الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <ProgressCard
            title="استخدام المخزون"
            current={totalProducts * 0.7}
            total={totalProducts}
            color="#005B48"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <ProgressCard
            title="هدف المبيعات الشهري"
            current={totalSales}
            total={100000}
            color="#1976d2"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <ProgressCard
            title="معدل رضا العملاء"
            current={85}
            total={100}
            color="#2e7d32"
          />
        </Grid>
      </Grid>

      {/* الصف الثالث */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <RecentActivity activities={recentActivities} />
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, borderRadius: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
              تنبيهات المخزون
            </Typography>
            <Box sx={{ mt: 2 }}>
              {lowStockProducts.length > 0 ? (
                lowStockProducts.map((product) => (
                  <Box
                    key={product.id}
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      py: 2,
                      borderBottom: '1px solid #f0f0f0'
                    }}
                  >
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                        {product.name}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        الكمية المتبقية: {product.quantity} {product.unit}
                      </Typography>
                    </Box>
                    <Chip
                      label="مخزون منخفض"
                      size="small"
                      color="error"
                    />
                  </Box>
                ))
              ) : (
                <Typography variant="body2" color="textSecondary" sx={{ textAlign: 'center', py: 4 }}>
                  لا توجد تنبيهات مخزون حالياً
                </Typography>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
