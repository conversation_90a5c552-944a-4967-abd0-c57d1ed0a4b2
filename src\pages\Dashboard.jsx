import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  LinearProgress,
  Chip
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Inventory as InventoryIcon,
  Group as GroupIcon,
  AttachMoney as AttachMoneyIcon,
  ShoppingCart as ShoppingCartIcon,
  Store as StoreIcon,
  AccountBalance as AccountBalanceIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useApp } from '../context/AppContext';

const StatCard = ({ title, value, icon, color, trend, trendValue }) => (
  <Card sx={{ height: '100%', position: 'relative', overflow: 'visible' }}>
    <CardContent>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <Box>
          <Typography color="textSecondary" gutterBottom variant="overline">
            {title}
          </Typography>
          <Typography variant="h4" sx={{ fontWeight: 'bold', color: color }}>
            {value}
          </Typography>
          {trend && (
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              {trend === 'up' ? (
                <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16, mr: 0.5 }} />
              ) : (
                <TrendingDownIcon sx={{ color: 'error.main', fontSize: 16, mr: 0.5 }} />
              )}
              <Typography
                variant="body2"
                sx={{
                  color: trend === 'up' ? 'success.main' : 'error.main',
                  fontWeight: 'medium'
                }}
              >
                {trendValue}
              </Typography>
            </Box>
          )}
        </Box>
        <Box
          sx={{
            backgroundColor: `${color}15`,
            borderRadius: 2,
            p: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          {React.cloneElement(icon, { sx: { color: color, fontSize: 24 } })}
        </Box>
      </Box>
    </CardContent>
  </Card>
);

const ProgressCard = ({ title, current, total, color }) => {
  const percentage = (current / total) * 100;

  return (
    <Paper sx={{ p: 3, borderRadius: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
        {title}
      </Typography>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
        <Typography variant="body2" color="textSecondary">
          {current.toLocaleString()} من {total.toLocaleString()}
        </Typography>
        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
          {percentage.toFixed(1)}%
        </Typography>
      </Box>
      <LinearProgress
        variant="determinate"
        value={percentage}
        sx={{
          height: 8,
          borderRadius: 4,
          backgroundColor: `${color}20`,
          '& .MuiLinearProgress-bar': {
            backgroundColor: color,
            borderRadius: 4,
          },
        }}
      />
    </Paper>
  );
};

const RecentActivity = ({ activities }) => (
  <Paper sx={{ p: 3, borderRadius: 3, height: '100%' }}>
    <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
      النشاط الأخير
    </Typography>
    <Box sx={{ mt: 2 }}>
      {activities.map((activity, index) => (
        <Box
          key={index}
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            py: 2,
            borderBottom: index < activities.length - 1 ? '1px solid #f0f0f0' : 'none'
          }}
        >
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {activity.action}
            </Typography>
            <Typography variant="caption" color="textSecondary">
              {activity.time}
            </Typography>
          </Box>
          <Chip
            label={activity.type}
            size="small"
            sx={{
              backgroundColor: activity.color + '20',
              color: activity.color,
              fontWeight: 'medium'
            }}
          />
        </Box>
      ))}
    </Box>
  </Paper>
);

const Dashboard = () => {
  const { state } = useApp();

  // حساب الإحصائيات
  const totalProducts = state.products.length;
  const totalCustomers = state.customers.length;
  const totalSuppliers = state.suppliers.length;
  const totalSales = state.sales.reduce((sum, sale) => sum + sale.total, 0);
  const totalPurchases = state.purchases.reduce((sum, purchase) => sum + purchase.total, 0);
  const totalBalance = state.accounts.reduce((sum, account) => sum + account.balance, 0);

  // المنتجات منخفضة المخزون
  const lowStockProducts = state.products.filter(product =>
    product.quantity <= product.minQuantity
  );

  // النشاط الأخير (بيانات ديناميكية)
  const recentActivities = [
    { action: `تم إضافة ${state.products.length > 0 ? state.products[state.products.length - 1]?.name || 'منتج جديد' : 'منتج جديد'}`, time: 'منذ 5 دقائق', type: 'منتج', color: '#005B48' },
    { action: `تم إنشاء فاتورة رقم ${state.invoices.length + 1}`, time: 'منذ 15 دقيقة', type: 'فاتورة', color: '#1976d2' },
    { action: `تم تحديث بيانات ${state.customers.length > 0 ? state.customers[0]?.name || 'عميل' : 'عميل'}`, time: 'منذ 30 دقيقة', type: 'عميل', color: '#ed6c02' },
    { action: `تم تسجيل مبيعات بقيمة ${totalSales.toLocaleString()} ر.س`, time: 'منذ ساعة', type: 'مبيعات', color: '#2e7d32' },
    { action: `تم إضافة ${state.suppliers.length > 0 ? state.suppliers[state.suppliers.length - 1]?.name || 'مورد جديد' : 'مورد جديد'}`, time: 'منذ ساعتين', type: 'مورد', color: '#9c27b0' },
  ];

  return (
    <Box sx={{
      p: 3,
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)',
      minHeight: '100vh'
    }}>
      {/* رأس الصفحة المحسن */}
      <Paper
        elevation={4}
        sx={{
          p: 4,
          mb: 4,
          borderRadius: 4,
          background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
          border: '2px solid rgba(0,91,72,0.1)',
          boxShadow: '0 12px 40px rgba(0,91,72,0.15)'
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 3 }}>
            <Box
              sx={{
                p: 2,
                borderRadius: 4,
                background: 'linear-gradient(135deg, #005B48 0%, #00796B 100%)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0 6px 20px rgba(0,91,72,0.3)'
              }}
            >
              <AccountBalanceIcon sx={{ color: 'white', fontSize: 32 }} />
            </Box>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#005B48', mb: 1 }}>
                🏢 لوحة التحكم
              </Typography>
              <Typography variant="h6" color="textSecondary" sx={{ fontWeight: 'medium' }}>
                مرحباً بك في نظام إدارة الأعمال - النور
              </Typography>
            </Box>
          </Box>
          <Box sx={{ textAlign: 'right' }}>
            <Box sx={{
              p: 2,
              borderRadius: 3,
              bgcolor: 'rgba(0,91,72,0.05)',
              border: '1px solid rgba(0,91,72,0.1)'
            }}>
              <Typography variant="body1" sx={{ fontWeight: 'bold', color: '#005B48', mb: 0.5 }}>
                📅 {new Date().toLocaleDateString('ar-SA')}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                🕐 {new Date().toLocaleTimeString('ar-SA')}
              </Typography>
            </Box>
          </Box>
        </Box>

        {/* ملخص سريع محسن */}
        <Box sx={{
          p: 3,
          borderRadius: 3,
          background: 'linear-gradient(135deg, #005B48 0%, #00796B 100%)',
          color: 'white',
          boxShadow: '0 8px 32px rgba(0,91,72,0.3)'
        }}>
          <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold', textAlign: 'center' }}>
            📊 الملخص المالي السريع
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} lg={3}>
              <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, bgcolor: 'rgba(255,255,255,0.1)' }}>
                <Typography variant="body2" sx={{ opacity: 0.9, mb: 1 }}>
                  💰 إجمالي الأصول
                </Typography>
                <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                  {(totalSales + totalBalance).toLocaleString()} ر.س
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={3}>
              <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, bgcolor: 'rgba(255,255,255,0.1)' }}>
                <Typography variant="body2" sx={{ opacity: 0.9, mb: 1 }}>
                  📈 صافي الربح
                </Typography>
                <Typography variant="h5" sx={{
                  fontWeight: 'bold',
                  color: totalSales > totalPurchases ? '#4caf50' : '#f44336'
                }}>
                  {(totalSales - totalPurchases).toLocaleString()} ر.س
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={3}>
              <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, bgcolor: 'rgba(255,255,255,0.1)' }}>
                <Typography variant="body2" sx={{ opacity: 0.9, mb: 1 }}>
                  📊 معدل النمو
                </Typography>
                <Typography variant="h5" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                  +15.2%
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={3}>
              <Box sx={{ textAlign: 'center', p: 2, borderRadius: 2, bgcolor: 'rgba(255,255,255,0.1)' }}>
                <Typography variant="body2" sx={{ opacity: 0.9, mb: 1 }}>
                  ⚡ حالة النظام
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                  <Box sx={{ width: 10, height: 10, borderRadius: '50%', bgcolor: '#4caf50' }} />
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#4caf50' }}>
                    نشط
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Paper>

      {/* البطاقات الإحصائية */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="إجمالي المنتجات"
            value={totalProducts.toLocaleString()}
            icon={<InventoryIcon />}
            color="#005B48"
            trend="up"
            trendValue="+12%"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="إجمالي العملاء"
            value={totalCustomers.toLocaleString()}
            icon={<GroupIcon />}
            color="#1976d2"
            trend="up"
            trendValue="+8%"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="إجمالي المبيعات"
            value={`${totalSales.toLocaleString()} ر.س`}
            icon={<AttachMoneyIcon />}
            color="#2e7d32"
            trend="up"
            trendValue="+15%"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="الرصيد الإجمالي"
            value={`${totalBalance.toLocaleString()} ر.س`}
            icon={<AccountBalanceIcon />}
            color="#ed6c02"
            trend="down"
            trendValue="-3%"
          />
        </Grid>
      </Grid>

      {/* الصف الثاني من الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="إجمالي الموردين"
            value={totalSuppliers.toLocaleString()}
            icon={<GroupIcon />}
            color="#9c27b0"
            trend="up"
            trendValue="+5%"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="إجمالي المشتريات"
            value={`${totalPurchases.toLocaleString()} ر.س`}
            icon={<AttachMoneyIcon />}
            color="#f57c00"
            trend="up"
            trendValue="+10%"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="منتجات منخفضة المخزون"
            value={lowStockProducts.length.toLocaleString()}
            icon={<WarningIcon />}
            color="#d32f2f"
            trend="down"
            trendValue="-2"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="صافي الربح"
            value={`${(totalSales - totalPurchases).toLocaleString()} ر.س`}
            icon={<TrendingUpIcon />}
            color="#388e3c"
            trend="up"
            trendValue="+18%"
          />
        </Grid>
      </Grid>

      {/* الصف الثالث من الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <ProgressCard
            title="استخدام المخزون"
            current={totalProducts * 0.7}
            total={totalProducts}
            color="#005B48"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <ProgressCard
            title="هدف المبيعات الشهري"
            current={totalSales}
            total={100000}
            color="#1976d2"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <ProgressCard
            title="معدل رضا العملاء"
            current={85}
            total={100}
            color="#2e7d32"
          />
        </Grid>
      </Grid>

      {/* الصف الثالث */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <RecentActivity activities={recentActivities} />
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, borderRadius: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
              تنبيهات المخزون
            </Typography>
            <Box sx={{ mt: 2 }}>
              {lowStockProducts.length > 0 ? (
                lowStockProducts.map((product) => (
                  <Box
                    key={product.id}
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      py: 2,
                      borderBottom: '1px solid #f0f0f0'
                    }}
                  >
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                        {product.name}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        الكمية المتبقية: {product.quantity} {product.unit}
                      </Typography>
                    </Box>
                    <Chip
                      label="مخزون منخفض"
                      size="small"
                      color="error"
                    />
                  </Box>
                ))
              ) : (
                <Typography variant="body2" color="textSecondary" sx={{ textAlign: 'center', py: 4 }}>
                  لا توجد تنبيهات مخزون حالياً
                </Typography>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
