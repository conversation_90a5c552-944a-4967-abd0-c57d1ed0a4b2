import React from 'react';
import {
  Box,
  Grid,
  Typography,
  Paper,
  LinearProgress,
  Chip
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  Inventory as InventoryIcon,
  Group as GroupIcon,
  AttachMoney as AttachMoneyIcon,
  ShoppingCart as ShoppingCartIcon,
  Store as StoreIcon,
  AccountBalance as AccountBalanceIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { useApp } from '../context/AppContext';
import StatCard from '../components/ui/GradientCard';



const ProgressCard = ({ title, current, total, color }) => {
  const percentage = (current / total) * 100;

  return (
    <Paper sx={{ p: 3, borderRadius: 3 }}>
      <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
        {title}
      </Typography>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
        <Typography variant="body2" color="textSecondary">
          {current.toLocaleString()} من {total.toLocaleString()}
        </Typography>
        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
          {percentage.toFixed(1)}%
        </Typography>
      </Box>
      <LinearProgress
        variant="determinate"
        value={percentage}
        sx={{
          height: 8,
          borderRadius: 4,
          backgroundColor: `${color}20`,
          '& .MuiLinearProgress-bar': {
            backgroundColor: color,
            borderRadius: 4,
          },
        }}
      />
    </Paper>
  );
};

const RecentActivity = ({ activities }) => (
  <Paper sx={{ p: 3, borderRadius: 3, height: '100%' }}>
    <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
      النشاط الأخير
    </Typography>
    <Box sx={{ mt: 2 }}>
      {activities.map((activity, index) => (
        <Box
          key={index}
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            py: 2,
            borderBottom: index < activities.length - 1 ? '1px solid #f0f0f0' : 'none'
          }}
        >
          <Box>
            <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
              {activity.action}
            </Typography>
            <Typography variant="caption" color="textSecondary">
              {activity.time}
            </Typography>
          </Box>
          <Chip
            label={activity.type}
            size="small"
            sx={{
              backgroundColor: activity.color + '20',
              color: activity.color,
              fontWeight: 'medium'
            }}
          />
        </Box>
      ))}
    </Box>
  </Paper>
);

const Dashboard = () => {
  const { state } = useApp();

  // حساب الإحصائيات
  const totalProducts = state.products.length;
  const totalCustomers = state.customers.length;
  const totalSuppliers = state.suppliers.length;
  const totalSales = state.sales.reduce((sum, sale) => sum + sale.total, 0);
  const totalPurchases = state.purchases.reduce((sum, purchase) => sum + purchase.total, 0);
  const totalBalance = state.accounts.reduce((sum, account) => sum + account.balance, 0);

  // المنتجات منخفضة المخزون
  const lowStockProducts = state.products.filter(product =>
    product.quantity <= product.minQuantity
  );

  // النشاط الأخير (بيانات ديناميكية)
  const recentActivities = [
    { action: `تم إضافة ${state.products.length > 0 ? state.products[state.products.length - 1]?.name || 'منتج جديد' : 'منتج جديد'}`, time: 'منذ 5 دقائق', type: 'منتج', color: '#005B48' },
    { action: `تم إنشاء فاتورة رقم ${state.invoices.length + 1}`, time: 'منذ 15 دقيقة', type: 'فاتورة', color: '#1976d2' },
    { action: `تم تحديث بيانات ${state.customers.length > 0 ? state.customers[0]?.name || 'عميل' : 'عميل'}`, time: 'منذ 30 دقيقة', type: 'عميل', color: '#ed6c02' },
    { action: `تم تسجيل مبيعات بقيمة ${totalSales.toLocaleString()} ر.س`, time: 'منذ ساعة', type: 'مبيعات', color: '#2e7d32' },
    { action: `تم إضافة ${state.suppliers.length > 0 ? state.suppliers[state.suppliers.length - 1]?.name || 'مورد جديد' : 'مورد جديد'}`, time: 'منذ ساعتين', type: 'مورد', color: '#9c27b0' },
  ];

  return (
    <Box sx={{ p: 3, minHeight: '100vh' }}>
      {/* رأس الصفحة البسيط */}
      <Paper sx={{ p: 3, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box
              sx={{
                p: 1.5,
                borderRadius: 1,
                backgroundColor: 'primary.main',
                color: 'primary.contrastText',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <AccountBalanceIcon sx={{ fontSize: 24 }} />
            </Box>
            <Box>
              <Typography variant="h4" sx={{ fontWeight: 600, mb: 0.5 }}>
                لوحة التحكم
              </Typography>
              <Typography variant="body1" color="text.secondary">
                مرحباً بك في نظام إدارة الأعمال - النور
              </Typography>
            </Box>
          </Box>
          <Box sx={{ textAlign: 'right' }}>
            <Typography variant="body2" color="text.secondary">
              {new Date().toLocaleDateString('ar-SA')}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {new Date().toLocaleTimeString('ar-SA')}
            </Typography>
          </Box>
        </Box>

        {/* ملخص سريع بسيط */}
        <Box sx={{
          p: 3,
          backgroundColor: 'background.light',
          borderRadius: 1,
          border: '1px solid',
          borderColor: 'divider'
        }}>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 500, textAlign: 'center' }}>
            الملخص المالي السريع
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} lg={3}>
              <Box sx={{ textAlign: 'center', p: 2 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  إجمالي الأصول
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  {(totalSales + totalBalance).toLocaleString()} ر.س
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={3}>
              <Box sx={{ textAlign: 'center', p: 2 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  صافي الربح
                </Typography>
                <Typography variant="h6" sx={{
                  fontWeight: 600,
                  color: totalSales > totalPurchases ? 'success.main' : 'error.main'
                }}>
                  {(totalSales - totalPurchases).toLocaleString()} ر.س
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={3}>
              <Box sx={{ textAlign: 'center', p: 2 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  معدل النمو
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 600, color: 'success.main' }}>
                  +15.2%
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6} lg={3}>
              <Box sx={{ textAlign: 'center', p: 2 }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  حالة النظام
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                  <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: 'success.main' }} />
                  <Typography variant="h6" sx={{ fontWeight: 600, color: 'success.main' }}>
                    نشط
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Paper>

      {/* البطاقات الإحصائية */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="إجمالي المنتجات"
            subtitle="عدد المنتجات المسجلة في النظام"
            value={totalProducts.toLocaleString()}
            icon={<InventoryIcon />}
            iconColor="#5F6368"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="إجمالي العملاء"
            subtitle="عدد العملاء المسجلين"
            value={totalCustomers.toLocaleString()}
            icon={<GroupIcon />}
            iconColor="#1976D2"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="إجمالي المبيعات"
            subtitle="إجمالي قيمة المبيعات"
            value={`${totalSales.toLocaleString()} ر.س`}
            icon={<AttachMoneyIcon />}
            iconColor="#388E3C"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="الرصيد الإجمالي"
            subtitle="إجمالي الأرصدة المتاحة"
            value={`${totalBalance.toLocaleString()} ر.س`}
            icon={<AccountBalanceIcon />}
            iconColor="#F57C00"
          />
        </Grid>
      </Grid>

      {/* الصف الثاني من الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="إجمالي الموردين"
            subtitle="عدد الموردين المسجلين"
            value={totalSuppliers.toLocaleString()}
            icon={<StoreIcon />}
            iconColor="#7B1FA2"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="إجمالي المشتريات"
            subtitle="إجمالي قيمة المشتريات"
            value={`${totalPurchases.toLocaleString()} ر.س`}
            icon={<ShoppingCartIcon />}
            iconColor="#D32F2F"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="تنبيهات المخزون"
            subtitle="منتجات منخفضة المخزون"
            value={lowStockProducts.length.toLocaleString()}
            icon={<WarningIcon />}
            iconColor="#F57C00"
          />
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <StatCard
            title="صافي الربح"
            subtitle="الربح الإجمالي المحقق"
            value={`${(totalSales - totalPurchases).toLocaleString()} ر.س`}
            icon={<TrendingUpIcon />}
            iconColor="#388E3C"
          />
        </Grid>
      </Grid>

      {/* الصف الثالث من الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <ProgressCard
            title="استخدام المخزون"
            current={totalProducts * 0.7}
            total={totalProducts}
            color="#005B48"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <ProgressCard
            title="هدف المبيعات الشهري"
            current={totalSales}
            total={100000}
            color="#1976d2"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <ProgressCard
            title="معدل رضا العملاء"
            current={85}
            total={100}
            color="#2e7d32"
          />
        </Grid>
      </Grid>

      {/* الصف الثالث */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <RecentActivity activities={recentActivities} />
        </Grid>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3, borderRadius: 3, height: '100%' }}>
            <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
              تنبيهات المخزون
            </Typography>
            <Box sx={{ mt: 2 }}>
              {lowStockProducts.length > 0 ? (
                lowStockProducts.map((product) => (
                  <Box
                    key={product.id}
                    sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      py: 2,
                      borderBottom: '1px solid #f0f0f0'
                    }}
                  >
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                        {product.name}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        الكمية المتبقية: {product.quantity} {product.unit}
                      </Typography>
                    </Box>
                    <Chip
                      label="مخزون منخفض"
                      size="small"
                      color="error"
                    />
                  </Box>
                ))
              ) : (
                <Typography variant="body2" color="textSecondary" sx={{ textAlign: 'center', py: 4 }}>
                  لا توجد تنبيهات مخزون حالياً
                </Typography>
              )}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
