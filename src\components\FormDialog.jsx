import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  IconButton
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

const FormDialog = ({ 
  open, 
  onClose, 
  onSubmit, 
  title,
  children,
  submitText = 'حفظ',
  cancelText = 'إلغاء',
  maxWidth = 'md',
  loading = false
}) => {
  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(e);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth={maxWidth}
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: 3,
          overflow: 'visible'
        }
      }}
    >
      <DialogTitle sx={{ 
        pb: 2, 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        borderBottom: '1px solid',
        borderColor: 'divider'
      }}>
        <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#005B48' }}>
          {title}
        </Typography>
        <IconButton 
          onClick={onClose}
          size="small"
          sx={{ 
            color: 'text.secondary',
            '&:hover': { 
              bgcolor: 'action.hover',
              color: 'text.primary'
            }
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      
      <Box component="form" onSubmit={handleSubmit}>
        <DialogContent sx={{ py: 3 }}>
          {children}
        </DialogContent>
        
        <DialogActions sx={{ 
          p: 3, 
          pt: 1,
          borderTop: '1px solid',
          borderColor: 'divider',
          gap: 2
        }}>
          <Button 
            onClick={onClose}
            variant="outlined"
            sx={{ 
              borderRadius: 2,
              px: 4,
              borderColor: '#e0e0e0',
              color: '#424242',
              '&:hover': {
                borderColor: '#bdbdbd',
                bgcolor: '#f5f7fa',
              }
            }}
          >
            {cancelText}
          </Button>
          <Button 
            type="submit"
            variant="contained"
            disabled={loading}
            sx={{ 
              borderRadius: 2,
              px: 4,
              bgcolor: '#005B48',
              '&:hover': { bgcolor: '#004a3b' }
            }}
          >
            {loading ? 'جاري الحفظ...' : submitText}
          </Button>
        </DialogActions>
      </Box>
    </Dialog>
  );
};

export default FormDialog;
