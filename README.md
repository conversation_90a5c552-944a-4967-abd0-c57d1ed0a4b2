# النور - تطبيق React احترافي

تطبيق ويب احترافي مبني باستخدام React وVite وMUI وReact Router.

## الميزات
- تصميم حديث ومتجاوب باستخدام MUI
- صفحات: الرئيسية، عن الموقع، تواصل، وخطأ 404
- مكونات قابلة لإعادة الاستخدام (Header, Footer, MainButton)
- دعم اللغة العربية واتجاه RTL

## بدء التشغيل

1. تثبيت الحزم:
   ```powershell
   npm install
   ```
2. تشغيل التطبيق:
   ```powershell
   npm run dev
   ```

## بنية المشروع
- `src/` : جميع الشيفرة المصدرية
- `Header.jsx` و`Footer.jsx`: مكونات رأس وتذييل قابلة لإعادة الاستخدام
- `Home.jsx`, `About.jsx`, `Contact.jsx`, `NotFound.jsx`: صفحات الموقع

## المتطلبات
- Node.js 18 أو أحدث

---
© 2025 النور. جميع الحقوق محفوظة.
