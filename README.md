# نظام النور لإدارة الأعمال

نظام شامل لإدارة الأعمال التجارية مبني بتقنيات React و Material-UI مع دعم كامل للغة العربية.

## المميزات الرئيسية

### 🏢 إدارة الشركة
- **لوحة التحكم**: عرض شامل للإحصائيات والمؤشرات الرئيسية
- **إدارة المنتجات**: إضافة وتعديل وحذف المنتجات مع أسعار متعددة
- **إدارة المخازن**: تتبع المخزون والسعات والاستخدام
- **إدارة العملاء**: قاعدة بيانات شاملة للعملاء
- **إدارة الموردين**: متابعة الموردين والتعاملات معهم

### 💰 النظام المالي
- **دليل الحسابات**: نظام محاسبي متكامل
- **إدارة المبيعات**: تسجيل ومتابعة عمليات البيع
- **إدارة المشتريات**: تسجيل ومتابعة عمليات الشراء
- **الفواتير**: إنشاء وطباعة الفواتير
- **التقارير المالية**: تقارير شاملة للأداء المالي

### 📊 التقارير والتحليلات
- **تقارير المنتجات**: تقارير مفصلة عن المخزون والمبيعات
- **تقارير العملاء**: تحليل سلوك العملاء والمبيعات
- **تقارير الموردين**: متابعة أداء الموردين
- **تقارير المخازن**: تحليل استخدام المخازن والسعات
- **التصدير والطباعة**: إمكانية تصدير التقارير بصيغ مختلفة

### 🔍 البحث والتصفية
- **البحث المتقدم**: بحث متقدم في جميع البيانات
- **التصفية الذكية**: تصفية البيانات حسب معايير متعددة
- **الفرز والترتيب**: ترتيب البيانات حسب الحاجة

### 🔐 الأمان والمصادقة
- **نظام تسجيل الدخول**: مصادقة آمنة للمستخدمين
- **إدارة الجلسات**: انتهاء صلاحية الجلسات تلقائياً
- **حماية المسارات**: حماية جميع صفحات النظام

### ⚙️ الإعدادات والتخصيص
- **إعدادات الشركة**: معلومات الشركة والشعار
- **إعدادات النظام**: تخصيص واجهة النظام
- **النسخ الاحتياطي**: إنشاء واستعادة النسخ الاحتياطية
- **الإشعارات**: إدارة الإشعارات والتنبيهات

## التقنيات المستخدمة

### Frontend
- **React 18**: مكتبة JavaScript لبناء واجهات المستخدم
- **Material-UI (MUI)**: مكتبة مكونات UI حديثة
- **React Router**: للتنقل بين الصفحات
- **Context API**: لإدارة الحالة العامة

### التصميم والواجهة
- **Material Design**: تصميم حديث ومتجاوب
- **RTL Support**: دعم كامل للغة العربية
- **Responsive Design**: متوافق مع جميع أحجام الشاشات

### إدارة البيانات
- **Local Storage**: تخزين البيانات محلياً
- **JSON Export/Import**: تصدير واستيراد البيانات
- **CSV Export**: تصدير التقارير بصيغة CSV

## التشغيل

1. تثبيت المتطلبات:
```bash
npm install
```

2. تشغيل التطبيق:
```bash
npm start
```

3. فتح المتصفح على: `http://localhost:3000`

## بيانات تسجيل الدخول التجريبية

```
اسم المستخدم: admin
كلمة المرور: admin123
```

## الصفحات المتاحة

- **تسجيل الدخول** (`/login`)
- **لوحة التحكم** (`/dashboard`)
- **المنتجات** (`/products`)
- **المخازن** (`/warehouses`)
- **العملاء** (`/customers`)
- **الموردين** (`/suppliers`)
- **المبيعات** (`/sales`)
- **المشتريات** (`/purchases`)
- **الفواتير** (`/invoices`)
- **الحسابات** (`/accounts`)
- **التقارير** (`/reports`)
- **الإعدادات** (`/settings`)

## المميزات التقنية

### الأداء
- تحميل كسول للصفحات (Lazy Loading)
- تحسين الذاكرة والأداء
- تخزين محلي فعال

### الأمان
- حماية المسارات
- انتهاء صلاحية الجلسات
- تشفير البيانات الحساسة

### سهولة الاستخدام
- واجهة بديهية وسهلة
- دعم كامل للعربية
- تصميم متجاوب
- إشعارات تفاعلية

---

**نظام النور لإدارة الأعمال** - حل شامل ومتكامل لإدارة أعمالك بكفاءة وسهولة.
