import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  InputAdornment,
  IconButton,
  Button,
  Chip,
  Card,
  CardContent,
  Grid,
  Tab,
  Tabs
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import PaymentsIcon from '@mui/icons-material/Payments';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';

// بيانات تجريبية للمعاملات المالية
const transactionsData = [
  {
    id: 1,
    date: '2025-05-26',
    type: 'إيراد',
    account: 'الصندوق الرئيسي',
    amount: 25000,
    description: 'إيرادات المبيعات',
    category: 'مبيعات'
  },
  {
    id: 2,
    date: '2025-05-25',
    type: 'مصروف',
    account: 'صندوق المصروفات',
    amount: 8500,
    description: 'مصروفات تشغيلية',
    category: 'مصروفات عامة'
  },
  {
    id: 3,
    date: '2025-05-24',
    type: 'إيراد',
    account: 'البنك الأهلي',
    amount: 15000,
    description: 'تحصيل ذمم مدينة',
    category: 'تحصيلات'
  }
];

function Finances() {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentTab, setCurrentTab] = useState(0);

  // حساب الإجماليات
  const totalRevenue = transactionsData
    .filter(t => t.type === 'إيراد')
    .reduce((sum, t) => sum + t.amount, 0);

  const totalExpenses = transactionsData
    .filter(t => t.type === 'مصروف')
    .reduce((sum, t) => sum + t.amount, 0);

  const handleTabChange = (_, newValue) => {
    setCurrentTab(newValue);
  };

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" sx={{ color: '#005B48', fontWeight: 'bold' }}>
          الماليات
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          sx={{
            backgroundColor: '#005B48',
            '&:hover': { backgroundColor: '#00483A' }
          }}
        >
          إضافة معاملة جديدة
        </Button>
      </Box>

      {/* البطاقات الإحصائية */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} lg={3}>
          <Card sx={{ bgcolor: '#005B48', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <PaymentsIcon fontSize="large" />
              <Box>
                <Typography variant="subtitle2">إجمالي الإيرادات</Typography>
                <Typography variant="h5">{totalRevenue.toLocaleString()} ر.س</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <Card sx={{ bgcolor: '#00796B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <AccountBalanceWalletIcon fontSize="large" />
              <Box>
                <Typography variant="subtitle2">إجمالي المصروفات</Typography>
                <Typography variant="h5">{totalExpenses.toLocaleString()} ر.س</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <Card sx={{ bgcolor: '#00897B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <TrendingUpIcon fontSize="large" />
              <Box>
                <Typography variant="subtitle2">صافي الربح</Typography>
                <Typography variant="h5">{(totalRevenue - totalExpenses).toLocaleString()} ر.س</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} lg={3}>
          <Card sx={{ bgcolor: '#26A69A', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <TrendingDownIcon fontSize="large" />
              <Box>
                <Typography variant="subtitle2">عدد المعاملات</Typography>
                <Typography variant="h5">{transactionsData.length}</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* التبويبات */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs
          value={currentTab}
          onChange={handleTabChange}
          sx={{
            '& .MuiTab-root': { fontSize: '1rem' },
            '& .Mui-selected': { color: '#005B48' },
            '& .MuiTabs-indicator': { bgcolor: '#005B48' }
          }}
        >
          <Tab label="كل المعاملات" />
          <Tab label="الإيرادات" />
          <Tab label="المصروفات" />
        </Tabs>
      </Box>

      {/* شريط البحث */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="بحث في المعاملات..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              bgcolor: '#f5f7fa',
              '&:hover fieldset': {
                borderColor: '#005B48',
              },
            },
          }}
        />
      </Box>

      {/* جدول المعاملات */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell>التاريخ</TableCell>
              <TableCell>نوع المعاملة</TableCell>
              <TableCell>الحساب</TableCell>
              <TableCell>المبلغ</TableCell>
              <TableCell>التصنيف</TableCell>
              <TableCell>الوصف</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {transactionsData
              .filter(transaction => {
                // فلترة حسب التبويب المحدد
                if (currentTab === 1) return transaction.type === 'إيراد';
                if (currentTab === 2) return transaction.type === 'مصروف';
                return true;
              })
              .filter(transaction =>
                transaction.description.includes(searchQuery) ||
                transaction.account.includes(searchQuery) ||
                transaction.category.includes(searchQuery)
              )
              .map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell>{transaction.date}</TableCell>
                  <TableCell>
                    <Chip
                      label={transaction.type}
                      size="small"
                      sx={{
                        bgcolor: transaction.type === 'إيراد' ? '#e8f5e9' : '#ffebee',
                        color: transaction.type === 'إيراد' ? '#2e7d32' : '#d32f2f',
                      }}
                    />
                  </TableCell>
                  <TableCell>{transaction.account}</TableCell>
                  <TableCell>
                    <Typography
                      sx={{
                        color: transaction.type === 'إيراد' ? '#2e7d32' : '#d32f2f',
                        fontWeight: 'medium'
                      }}
                    >
                      {transaction.amount.toLocaleString()} ر.س
                    </Typography>
                  </TableCell>
                  <TableCell>{transaction.category}</TableCell>
                  <TableCell>{transaction.description}</TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <IconButton size="small" color="primary">
                        <EditIcon />
                      </IconButton>
                      <IconButton size="small" color="error">
                        <DeleteIcon />
                      </IconButton>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}

export default Finances;
