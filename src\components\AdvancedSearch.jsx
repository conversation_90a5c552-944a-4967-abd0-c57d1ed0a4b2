import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  IconButton,
  Chip
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';

const AdvancedSearch = ({ 
  open, 
  onClose, 
  onSearch, 
  fields = [],
  title = 'البحث المتقدم'
}) => {
  const [searchCriteria, setSearchCriteria] = useState({});
  const [activeFilters, setActiveFilters] = useState([]);

  const handleFieldChange = (fieldName, value) => {
    setSearchCriteria(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const handleSearch = () => {
    // تصفية القيم الفارغة
    const filteredCriteria = Object.entries(searchCriteria)
      .filter(([key, value]) => value && value.toString().trim() !== '')
      .reduce((acc, [key, value]) => {
        acc[key] = value;
        return acc;
      }, {});

    // إنشاء قائمة الفلاتر النشطة
    const filters = Object.entries(filteredCriteria).map(([key, value]) => {
      const field = fields.find(f => f.name === key);
      return {
        key,
        label: field?.label || key,
        value: value.toString()
      };
    });

    setActiveFilters(filters);
    onSearch(filteredCriteria);
  };

  const handleClear = () => {
    setSearchCriteria({});
    setActiveFilters([]);
    onSearch({});
  };

  const removeFilter = (filterKey) => {
    const newCriteria = { ...searchCriteria };
    delete newCriteria[filterKey];
    setSearchCriteria(newCriteria);
    
    const newFilters = activeFilters.filter(f => f.key !== filterKey);
    setActiveFilters(newFilters);
    
    onSearch(newCriteria);
  };

  const renderField = (field) => {
    const value = searchCriteria[field.name] || '';

    switch (field.type) {
      case 'select':
        return (
          <FormControl fullWidth margin="normal">
            <InputLabel>{field.label}</InputLabel>
            <Select
              value={value}
              onChange={(e) => handleFieldChange(field.name, e.target.value)}
              label={field.label}
            >
              <MenuItem value="">الكل</MenuItem>
              {field.options?.map((option) => (
                <MenuItem key={option.value} value={option.value}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );

      case 'number':
        return (
          <TextField
            fullWidth
            margin="normal"
            label={field.label}
            type="number"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            InputProps={{
              inputProps: { min: field.min || 0, max: field.max }
            }}
          />
        );

      case 'date':
        return (
          <TextField
            fullWidth
            margin="normal"
            label={field.label}
            type="date"
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            InputLabelProps={{
              shrink: true,
            }}
          />
        );

      case 'dateRange':
        return (
          <Grid container spacing={2}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                margin="normal"
                label={`${field.label} - من`}
                type="date"
                value={searchCriteria[`${field.name}_from`] || ''}
                onChange={(e) => handleFieldChange(`${field.name}_from`, e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                margin="normal"
                label={`${field.label} - إلى`}
                type="date"
                value={searchCriteria[`${field.name}_to`] || ''}
                onChange={(e) => handleFieldChange(`${field.name}_to`, e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>
          </Grid>
        );

      default: // text
        return (
          <TextField
            fullWidth
            margin="normal"
            label={field.label}
            value={value}
            onChange={(e) => handleFieldChange(field.name, e.target.value)}
            placeholder={field.placeholder}
          />
        );
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: 3
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        borderBottom: '1px solid',
        borderColor: 'divider'
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SearchIcon sx={{ color: '#005B48' }} />
          <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#005B48' }}>
            {title}
          </Typography>
        </Box>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ py: 3 }}>
        {/* الفلاتر النشطة */}
        {activeFilters.length > 0 && (
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 'bold' }}>
              الفلاتر النشطة:
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {activeFilters.map((filter) => (
                <Chip
                  key={filter.key}
                  label={`${filter.label}: ${filter.value}`}
                  onDelete={() => removeFilter(filter.key)}
                  sx={{
                    bgcolor: '#e3f2fd',
                    color: '#1976d2',
                    '& .MuiChip-deleteIcon': {
                      color: '#1976d2'
                    }
                  }}
                />
              ))}
            </Box>
          </Box>
        )}

        {/* حقول البحث */}
        <Grid container spacing={2}>
          {fields.map((field) => (
            <Grid item xs={12} sm={field.width || 6} key={field.name}>
              {renderField(field)}
            </Grid>
          ))}
        </Grid>
      </DialogContent>

      <DialogActions sx={{ 
        p: 3, 
        borderTop: '1px solid',
        borderColor: 'divider',
        gap: 2
      }}>
        <Button
          onClick={handleClear}
          variant="outlined"
          startIcon={<ClearIcon />}
          sx={{
            borderColor: '#e0e0e0',
            color: '#424242',
            '&:hover': {
              borderColor: '#bdbdbd',
              bgcolor: '#f5f7fa',
            }
          }}
        >
          مسح الكل
        </Button>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{
            borderColor: '#e0e0e0',
            color: '#424242',
            '&:hover': {
              borderColor: '#bdbdbd',
              bgcolor: '#f5f7fa',
            }
          }}
        >
          إلغاء
        </Button>
        <Button
          onClick={handleSearch}
          variant="contained"
          startIcon={<SearchIcon />}
          sx={{
            bgcolor: '#005B48',
            '&:hover': { bgcolor: '#004a3b' }
          }}
        >
          بحث
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default AdvancedSearch;
