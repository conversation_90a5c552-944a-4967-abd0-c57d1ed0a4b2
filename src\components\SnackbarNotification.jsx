import React from 'react';
import {
  Snack<PERSON>,
  Alert,
  AlertTitle
} from '@mui/material';

const SnackbarNotification = ({ 
  open, 
  onClose, 
  message, 
  severity = 'success',
  title,
  autoHideDuration = 6000
}) => {
  return (
    <Snackbar
      open={open}
      autoHideDuration={autoHideDuration}
      onClose={onClose}
      anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
    >
      <Alert 
        onClose={onClose} 
        severity={severity}
        sx={{ 
          width: '100%',
          borderRadius: 2,
          '& .MuiAlert-message': {
            width: '100%'
          }
        }}
      >
        {title && <AlertTitle>{title}</AlertTitle>}
        {message}
      </Alert>
    </Snackbar>
  );
};

export default SnackbarNotification;
