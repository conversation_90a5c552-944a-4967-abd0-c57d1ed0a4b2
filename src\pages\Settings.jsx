import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Divider,
  Card,
  CardContent,
  CardActions,
  Alert,
  Tabs,
  Tab
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import RestoreIcon from '@mui/icons-material/Restore';
import SecurityIcon from '@mui/icons-material/Security';
import BusinessIcon from '@mui/icons-material/Business';
import NotificationsIcon from '@mui/icons-material/Notifications';
import BackupIcon from '@mui/icons-material/Backup';
import SnackbarNotification from '../components/SnackbarNotification';

const TabPanel = ({ children, value, index, ...other }) => (
  <div
    role="tabpanel"
    hidden={value !== index}
    id={`settings-tabpanel-${index}`}
    aria-labelledby={`settings-tab-${index}`}
    {...other}
  >
    {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
  </div>
);

const Settings = () => {
  const [currentTab, setCurrentTab] = useState(0);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  
  // إعدادات الشركة
  const [companySettings, setCompanySettings] = useState({
    name: 'شركة النور للتجارة',
    address: 'الرياض، المملكة العربية السعودية',
    phone: '+966 11 123 4567',
    email: '<EMAIL>',
    taxNumber: '*********',
    logo: ''
  });

  // إعدادات النظام
  const [systemSettings, setSystemSettings] = useState({
    autoSave: true,
    notifications: true,
    darkMode: false,
    language: 'ar',
    currency: 'SAR',
    dateFormat: 'dd/mm/yyyy'
  });

  // إعدادات الأمان
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    sessionTimeout: 30,
    passwordExpiry: 90,
    loginAttempts: 5
  });

  const handleTabChange = (event, newValue) => {
    setCurrentTab(newValue);
  };

  const handleCompanyChange = (field, value) => {
    setCompanySettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSystemChange = (field, value) => {
    setSystemSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSecurityChange = (field, value) => {
    setSecuritySettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const saveSettings = () => {
    // حفظ الإعدادات في localStorage
    localStorage.setItem('alnour-company-settings', JSON.stringify(companySettings));
    localStorage.setItem('alnour-system-settings', JSON.stringify(systemSettings));
    localStorage.setItem('alnour-security-settings', JSON.stringify(securitySettings));
    
    setNotification({
      open: true,
      message: 'تم حفظ الإعدادات بنجاح',
      severity: 'success'
    });
  };

  const resetSettings = () => {
    // إعادة تعيين الإعدادات للقيم الافتراضية
    setCompanySettings({
      name: 'شركة النور للتجارة',
      address: 'الرياض، المملكة العربية السعودية',
      phone: '+966 11 123 4567',
      email: '<EMAIL>',
      taxNumber: '*********',
      logo: ''
    });

    setSystemSettings({
      autoSave: true,
      notifications: true,
      darkMode: false,
      language: 'ar',
      currency: 'SAR',
      dateFormat: 'dd/mm/yyyy'
    });

    setSecuritySettings({
      twoFactorAuth: false,
      sessionTimeout: 30,
      passwordExpiry: 90,
      loginAttempts: 5
    });

    setNotification({
      open: true,
      message: 'تم إعادة تعيين الإعدادات للقيم الافتراضية',
      severity: 'info'
    });
  };

  const exportData = () => {
    const allData = {
      company: companySettings,
      system: systemSettings,
      security: securitySettings,
      exportDate: new Date().toISOString()
    };

    const dataStr = JSON.stringify(allData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `alnour-settings-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);

    setNotification({
      open: true,
      message: 'تم تصدير الإعدادات بنجاح',
      severity: 'success'
    });
  };

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', color: '#005B48', mb: 1 }}>
          الإعدادات
        </Typography>
        <Typography variant="body1" color="textSecondary">
          إدارة إعدادات النظام والشركة
        </Typography>
      </Box>

      <Paper sx={{ borderRadius: 3 }}>
        {/* التبويبات */}
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={currentTab} onChange={handleTabChange}>
            <Tab 
              icon={<BusinessIcon />} 
              label="معلومات الشركة" 
              iconPosition="start"
            />
            <Tab 
              icon={<NotificationsIcon />} 
              label="إعدادات النظام" 
              iconPosition="start"
            />
            <Tab 
              icon={<SecurityIcon />} 
              label="الأمان" 
              iconPosition="start"
            />
            <Tab 
              icon={<BackupIcon />} 
              label="النسخ الاحتياطي" 
              iconPosition="start"
            />
          </Tabs>
        </Box>

        {/* تبويب معلومات الشركة */}
        <TabPanel value={currentTab} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="اسم الشركة"
                value={companySettings.name}
                onChange={(e) => handleCompanyChange('name', e.target.value)}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="رقم الهاتف"
                value={companySettings.phone}
                onChange={(e) => handleCompanyChange('phone', e.target.value)}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="البريد الإلكتروني"
                type="email"
                value={companySettings.email}
                onChange={(e) => handleCompanyChange('email', e.target.value)}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="الرقم الضريبي"
                value={companySettings.taxNumber}
                onChange={(e) => handleCompanyChange('taxNumber', e.target.value)}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="العنوان"
                multiline
                rows={3}
                value={companySettings.address}
                onChange={(e) => handleCompanyChange('address', e.target.value)}
                margin="normal"
              />
            </Grid>
          </Grid>
        </TabPanel>

        {/* تبويب إعدادات النظام */}
        <TabPanel value={currentTab} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    الإعدادات العامة
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemSettings.autoSave}
                          onChange={(e) => handleSystemChange('autoSave', e.target.checked)}
                        />
                      }
                      label="الحفظ التلقائي"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemSettings.notifications}
                          onChange={(e) => handleSystemChange('notifications', e.target.checked)}
                        />
                      }
                      label="الإشعارات"
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={systemSettings.darkMode}
                          onChange={(e) => handleSystemChange('darkMode', e.target.checked)}
                        />
                      }
                      label="الوضع المظلم"
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* تبويب الأمان */}
        <TabPanel value={currentTab} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Alert severity="info" sx={{ mb: 3 }}>
                إعدادات الأمان تساعد في حماية بياناتك وضمان أمان النظام
              </Alert>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    المصادقة
                  </Typography>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={securitySettings.twoFactorAuth}
                        onChange={(e) => handleSecurityChange('twoFactorAuth', e.target.checked)}
                      />
                    }
                    label="المصادقة الثنائية"
                  />
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    إعدادات الجلسة
                  </Typography>
                  <TextField
                    fullWidth
                    label="مهلة انتهاء الجلسة (دقيقة)"
                    type="number"
                    value={securitySettings.sessionTimeout}
                    onChange={(e) => handleSecurityChange('sessionTimeout', parseInt(e.target.value))}
                    margin="normal"
                  />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* تبويب النسخ الاحتياطي */}
        <TabPanel value={currentTab} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Alert severity="warning" sx={{ mb: 3 }}>
                يُنصح بإنشاء نسخة احتياطية من بياناتك بانتظام
              </Alert>
            </Grid>
            <Grid item xs={12} md={6}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    تصدير البيانات
                  </Typography>
                  <Typography variant="body2" color="textSecondary" paragraph>
                    تصدير جميع إعدادات النظام إلى ملف JSON
                  </Typography>
                </CardContent>
                <CardActions>
                  <Button
                    variant="contained"
                    startIcon={<BackupIcon />}
                    onClick={exportData}
                    sx={{ bgcolor: '#005B48', '&:hover': { bgcolor: '#004a3b' } }}
                  >
                    تصدير الإعدادات
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* أزرار الحفظ */}
        <Divider />
        <Box sx={{ p: 3, display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
          <Button
            variant="outlined"
            startIcon={<RestoreIcon />}
            onClick={resetSettings}
            sx={{
              borderColor: '#e0e0e0',
              color: '#424242',
              '&:hover': {
                borderColor: '#bdbdbd',
                bgcolor: '#f5f7fa',
              }
            }}
          >
            إعادة تعيين
          </Button>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={saveSettings}
            sx={{ bgcolor: '#005B48', '&:hover': { bgcolor: '#004a3b' } }}
          >
            حفظ الإعدادات
          </Button>
        </Box>
      </Paper>

      {/* إشعارات */}
      <SnackbarNotification
        open={notification.open}
        onClose={() => setNotification({ ...notification, open: false })}
        message={notification.message}
        severity={notification.severity}
      />
    </Box>
  );
};

export default Settings;
