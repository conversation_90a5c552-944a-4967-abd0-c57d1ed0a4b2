import React, { createContext, useContext, useReducer, useEffect } from 'react';

// البيانات الأولية
const initialState = {
  products: [
    {
      id: 1,
      code: 'ksu123',
      name: 'جهاز كمبيوتر أيسر أسباير',
      category: 'أجهزة كمبيوتر',
      brand: 'Acer',
      purchasePrice: 60000,
      salePrice: 65999,
      wholesalePrice: 62000,
      distributorPrice: 60200,
      quantity: 1,
      unit: 'قطعة',
      minQuantity: 1,
      description: 'جهاز كمبيوتر محمول عالي الأداء'
    },
  ],
  customers: [
    { id: 1, name: 'أحمد محمد', phone: '0123456789', email: '<EMAIL>', balance: 5000, address: 'الرياض' },
    { id: 2, name: 'محمد علي', phone: '0123456788', email: '<EMAIL>', balance: 3500, address: 'جدة' },
    { id: 3, name: 'سارة أحمد', phone: '0123456787', email: '<EMAIL>', balance: 7000, address: 'الدمام' },
  ],
  suppliers: [
    {
      id: 1,
      name: 'شركة الأمل للتوريدات',
      contact: 'محمد عبدالله',
      phone: '0123456789',
      email: '<EMAIL>',
      balance: 15000,
      address: 'الرياض'
    },
    {
      id: 2,
      name: 'مؤسسة النور التجارية',
      contact: 'أحمد محمود',
      phone: '0123456788',
      email: '<EMAIL>',
      balance: 8500,
      address: 'جدة'
    },
  ],
  warehouses: [
    {
      id: 1,
      name: 'المخزن الرئيسي',
      location: 'المنطقة الصناعية',
      capacity: 1000,
      used: 650,
      status: 'نشط',
      manager: 'خالد أحمد'
    },
    {
      id: 2,
      name: 'مخزن الفرع الأول',
      location: 'وسط المدينة',
      capacity: 500,
      used: 320,
      status: 'نشط',
      manager: 'محمد علي'
    },
  ],
  stores: [
    {
      id: 1,
      name: 'فرع المدينة',
      location: 'شارع الملك فهد',
      manager: 'خالد محمد',
      employees: 5,
      status: 'نشط',
      phone: '**********'
    },
    {
      id: 2,
      name: 'فرع الشمال',
      location: 'حي النزهة',
      manager: 'أحمد علي',
      employees: 4,
      status: 'نشط',
      phone: '**********'
    },
  ],
  accounts: [
    {
      id: 1,
      name: 'الصندوق الرئيسي',
      type: 'صندوق',
      balance: 50000,
      lastTransaction: '2025-05-26',
      status: 'نشط'
    },
    {
      id: 2,
      name: 'البنك الأهلي',
      type: 'بنك',
      balance: 150000,
      lastTransaction: '2025-05-25',
      status: 'نشط'
    },
  ],
  invoices: [
    {
      id: 1,
      number: 'INV-001',
      date: '2025-05-26',
      customer: 'شركة النور للتجارة',
      total: 15000,
      status: 'مدفوعة',
      type: 'بيع',
      items: []
    },
    {
      id: 2,
      number: 'INV-002',
      date: '2025-05-25',
      customer: 'مؤسسة الأمل',
      total: 8500,
      status: 'معلقة',
      type: 'شراء',
      items: []
    },
  ],
  sales: [
    {
      id: 1,
      date: '2025-05-25',
      customer: 'أحمد محمد',
      invoice: 'SALE-001',
      total: 3500,
      status: 'مكتمل',
      paymentStatus: 'مدفوع'
    },
    {
      id: 2,
      date: '2025-05-24',
      customer: 'محمد علي',
      invoice: 'SALE-002',
      total: 2800,
      status: 'مكتمل',
      paymentStatus: 'جزئي'
    },
  ],
  purchases: [
    {
      id: 1,
      date: '2025-05-25',
      supplier: 'شركة الأمل للتوريدات',
      invoice: 'INV-001',
      total: 25000,
      status: 'مكتمل'
    },
    {
      id: 2,
      date: '2025-05-24',
      supplier: 'مؤسسة النور التجارية',
      invoice: 'INV-002',
      total: 18500,
      status: 'قيد التنفيذ'
    },
  ],
  transactions: [
    {
      id: 1,
      date: '2025-05-26',
      type: 'إيراد',
      category: 'مبيعات',
      amount: 15000,
      description: 'بيع منتجات',
      account: 'الصندوق الرئيسي'
    },
    {
      id: 2,
      date: '2025-05-25',
      type: 'مصروف',
      category: 'مشتريات',
      amount: 8500,
      description: 'شراء بضائع',
      account: 'البنك الأهلي'
    },
  ]
};

// أنواع الإجراءات
const actionTypes = {
  // المنتجات
  ADD_PRODUCT: 'ADD_PRODUCT',
  UPDATE_PRODUCT: 'UPDATE_PRODUCT',
  DELETE_PRODUCT: 'DELETE_PRODUCT',

  // العملاء
  ADD_CUSTOMER: 'ADD_CUSTOMER',
  UPDATE_CUSTOMER: 'UPDATE_CUSTOMER',
  DELETE_CUSTOMER: 'DELETE_CUSTOMER',

  // الموردين
  ADD_SUPPLIER: 'ADD_SUPPLIER',
  UPDATE_SUPPLIER: 'UPDATE_SUPPLIER',
  DELETE_SUPPLIER: 'DELETE_SUPPLIER',

  // المخازن
  ADD_WAREHOUSE: 'ADD_WAREHOUSE',
  UPDATE_WAREHOUSE: 'UPDATE_WAREHOUSE',
  DELETE_WAREHOUSE: 'DELETE_WAREHOUSE',

  // المتاجر
  ADD_STORE: 'ADD_STORE',
  UPDATE_STORE: 'UPDATE_STORE',
  DELETE_STORE: 'DELETE_STORE',

  // الحسابات
  ADD_ACCOUNT: 'ADD_ACCOUNT',
  UPDATE_ACCOUNT: 'UPDATE_ACCOUNT',
  DELETE_ACCOUNT: 'DELETE_ACCOUNT',

  // الفواتير
  ADD_INVOICE: 'ADD_INVOICE',
  UPDATE_INVOICE: 'UPDATE_INVOICE',
  DELETE_INVOICE: 'DELETE_INVOICE',

  // المبيعات
  ADD_SALE: 'ADD_SALE',
  UPDATE_SALE: 'UPDATE_SALE',
  DELETE_SALE: 'DELETE_SALE',

  // المشتريات
  ADD_PURCHASE: 'ADD_PURCHASE',
  UPDATE_PURCHASE: 'UPDATE_PURCHASE',
  DELETE_PURCHASE: 'DELETE_PURCHASE',

  // المعاملات المالية
  ADD_TRANSACTION: 'ADD_TRANSACTION',
  UPDATE_TRANSACTION: 'UPDATE_TRANSACTION',
  DELETE_TRANSACTION: 'DELETE_TRANSACTION',

  // تحميل البيانات
  LOAD_DATA: 'LOAD_DATA',
  SAVE_DATA: 'SAVE_DATA'
};

// Reducer function
const appReducer = (state, action) => {
  switch (action.type) {
    // المنتجات
    case actionTypes.ADD_PRODUCT:
      return {
        ...state,
        products: [...state.products, { ...action.payload, id: Date.now() }]
      };
    case actionTypes.UPDATE_PRODUCT:
      return {
        ...state,
        products: state.products.map(item =>
          item.id === action.payload.id ? action.payload : item
        )
      };
    case actionTypes.DELETE_PRODUCT:
      return {
        ...state,
        products: state.products.filter(item => item.id !== action.payload)
      };

    // العملاء
    case actionTypes.ADD_CUSTOMER:
      return {
        ...state,
        customers: [...state.customers, { ...action.payload, id: Date.now() }]
      };
    case actionTypes.UPDATE_CUSTOMER:
      return {
        ...state,
        customers: state.customers.map(item =>
          item.id === action.payload.id ? action.payload : item
        )
      };
    case actionTypes.DELETE_CUSTOMER:
      return {
        ...state,
        customers: state.customers.filter(item => item.id !== action.payload)
      };

    // الموردين
    case actionTypes.ADD_SUPPLIER:
      return {
        ...state,
        suppliers: [...state.suppliers, { ...action.payload, id: Date.now() }]
      };
    case actionTypes.UPDATE_SUPPLIER:
      return {
        ...state,
        suppliers: state.suppliers.map(item =>
          item.id === action.payload.id ? action.payload : item
        )
      };
    case actionTypes.DELETE_SUPPLIER:
      return {
        ...state,
        suppliers: state.suppliers.filter(item => item.id !== action.payload)
      };

    // المخازن
    case actionTypes.ADD_WAREHOUSE:
      return {
        ...state,
        warehouses: [...state.warehouses, { ...action.payload, id: Date.now() }]
      };
    case actionTypes.UPDATE_WAREHOUSE:
      return {
        ...state,
        warehouses: state.warehouses.map(item =>
          item.id === action.payload.id ? action.payload : item
        )
      };
    case actionTypes.DELETE_WAREHOUSE:
      return {
        ...state,
        warehouses: state.warehouses.filter(item => item.id !== action.payload)
      };

    // تحميل البيانات
    case actionTypes.LOAD_DATA:
      return action.payload;

    default:
      return state;
  }
};

// إنشاء Context
const AppContext = createContext();

// Provider Component
export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // تحميل البيانات من localStorage عند بدء التطبيق
  useEffect(() => {
    const savedData = localStorage.getItem('alnour-app-data');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        dispatch({ type: actionTypes.LOAD_DATA, payload: parsedData });
      } catch (error) {
        console.error('Error loading data from localStorage:', error);
      }
    }
  }, []);

  // حفظ البيانات في localStorage عند تغيير الحالة
  useEffect(() => {
    localStorage.setItem('alnour-app-data', JSON.stringify(state));
  }, [state]);

  // وظائف المنتجات
  const addProduct = (product) => {
    dispatch({ type: actionTypes.ADD_PRODUCT, payload: product });
  };

  const updateProduct = (product) => {
    dispatch({ type: actionTypes.UPDATE_PRODUCT, payload: product });
  };

  const deleteProduct = (id) => {
    dispatch({ type: actionTypes.DELETE_PRODUCT, payload: id });
  };

  // وظائف العملاء
  const addCustomer = (customer) => {
    dispatch({ type: actionTypes.ADD_CUSTOMER, payload: customer });
  };

  const updateCustomer = (customer) => {
    dispatch({ type: actionTypes.UPDATE_CUSTOMER, payload: customer });
  };

  const deleteCustomer = (id) => {
    dispatch({ type: actionTypes.DELETE_CUSTOMER, payload: id });
  };

  // وظائف الموردين
  const addSupplier = (supplier) => {
    dispatch({ type: actionTypes.ADD_SUPPLIER, payload: supplier });
  };

  const updateSupplier = (supplier) => {
    dispatch({ type: actionTypes.UPDATE_SUPPLIER, payload: supplier });
  };

  const deleteSupplier = (id) => {
    dispatch({ type: actionTypes.DELETE_SUPPLIER, payload: id });
  };

  // وظائف المخازن
  const addWarehouse = (warehouse) => {
    dispatch({ type: actionTypes.ADD_WAREHOUSE, payload: warehouse });
  };

  const updateWarehouse = (warehouse) => {
    dispatch({ type: actionTypes.UPDATE_WAREHOUSE, payload: warehouse });
  };

  const deleteWarehouse = (id) => {
    dispatch({ type: actionTypes.DELETE_WAREHOUSE, payload: id });
  };

  const value = {
    state,
    // وظائف المنتجات
    addProduct,
    updateProduct,
    deleteProduct,
    // وظائف العملاء
    addCustomer,
    updateCustomer,
    deleteCustomer,
    // وظائف الموردين
    addSupplier,
    updateSupplier,
    deleteSupplier,
    // وظائف المخازن
    addWarehouse,
    updateWarehouse,
    deleteWarehouse,
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};

// Hook لاستخدام Context
export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

export { actionTypes };
export default AppContext;
