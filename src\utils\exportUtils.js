// وظائف التصدير والطباعة

// تصدير البيانات إلى CSV
export const exportToCSV = (data, filename) => {
  if (!data || data.length === 0) {
    alert('لا توجد بيانات للتصدير');
    return;
  }

  // الحصول على أسماء الأعمدة
  const headers = Object.keys(data[0]);
  
  // تحويل البيانات إلى CSV
  const csvContent = [
    headers.join(','), // رأس الجدول
    ...data.map(row => 
      headers.map(header => {
        const value = row[header];
        // التعامل مع القيم التي تحتوي على فواصل أو علامات اقتباس
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',')
    )
  ].join('\n');

  // إنشاء ملف وتحميله
  const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// تصدير البيانات إلى JSON
export const exportToJSON = (data, filename) => {
  if (!data) {
    alert('لا توجد بيانات للتصدير');
    return;
  }

  const jsonContent = JSON.stringify(data, null, 2);
  const blob = new Blob([jsonContent], { type: 'application/json' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.json`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// طباعة الفاتورة
export const printInvoice = (invoice) => {
  const printWindow = window.open('', '_blank');
  const currentDate = new Date().toLocaleDateString('ar-SA');
  
  const htmlContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>فاتورة ${invoice.number}</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          margin: 0;
          padding: 20px;
          background: white;
          color: #333;
          direction: rtl;
        }
        .invoice-header {
          text-align: center;
          border-bottom: 3px solid #005B48;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .company-name {
          font-size: 28px;
          font-weight: bold;
          color: #005B48;
          margin-bottom: 10px;
        }
        .invoice-title {
          font-size: 24px;
          color: #666;
          margin-bottom: 5px;
        }
        .invoice-info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 30px;
        }
        .info-section {
          flex: 1;
        }
        .info-title {
          font-weight: bold;
          color: #005B48;
          margin-bottom: 10px;
          font-size: 16px;
        }
        .info-content {
          line-height: 1.6;
        }
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
        }
        .items-table th,
        .items-table td {
          border: 1px solid #ddd;
          padding: 12px;
          text-align: center;
        }
        .items-table th {
          background-color: #005B48;
          color: white;
          font-weight: bold;
        }
        .items-table tr:nth-child(even) {
          background-color: #f9f9f9;
        }
        .total-section {
          text-align: left;
          margin-top: 20px;
        }
        .total-row {
          display: flex;
          justify-content: space-between;
          padding: 8px 0;
          border-bottom: 1px solid #eee;
        }
        .total-row.final {
          font-weight: bold;
          font-size: 18px;
          border-bottom: 3px solid #005B48;
          color: #005B48;
        }
        .footer {
          margin-top: 40px;
          text-align: center;
          color: #666;
          font-size: 14px;
        }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="invoice-header">
        <div class="company-name">شركة النور للتجارة</div>
        <div class="invoice-title">فاتورة ${invoice.type}</div>
        <div>رقم الفاتورة: ${invoice.number}</div>
      </div>

      <div class="invoice-info">
        <div class="info-section">
          <div class="info-title">معلومات الفاتورة:</div>
          <div class="info-content">
            <div>التاريخ: ${invoice.date}</div>
            <div>النوع: ${invoice.type}</div>
            <div>الحالة: ${invoice.status}</div>
          </div>
        </div>
        <div class="info-section">
          <div class="info-title">${invoice.type === 'بيع' ? 'بيانات العميل:' : 'بيانات المورد:'}</div>
          <div class="info-content">
            <div>${invoice.customer}</div>
          </div>
        </div>
        <div class="info-section">
          <div class="info-title">تاريخ الطباعة:</div>
          <div class="info-content">${currentDate}</div>
        </div>
      </div>

      <table class="items-table">
        <thead>
          <tr>
            <th>م</th>
            <th>الصنف</th>
            <th>الكمية</th>
            <th>السعر</th>
            <th>الإجمالي</th>
          </tr>
        </thead>
        <tbody>
          ${invoice.items && invoice.items.length > 0 ? 
            invoice.items.map((item, index) => `
              <tr>
                <td>${index + 1}</td>
                <td>${item.name || 'غير محدد'}</td>
                <td>${item.quantity || 1}</td>
                <td>${(item.price || 0).toLocaleString()} ر.س</td>
                <td>${((item.quantity || 1) * (item.price || 0)).toLocaleString()} ر.س</td>
              </tr>
            `).join('') :
            '<tr><td colspan="5">لا توجد عناصر في الفاتورة</td></tr>'
          }
        </tbody>
      </table>

      <div class="total-section">
        <div class="total-row">
          <span>المجموع الفرعي:</span>
          <span>${(invoice.total * 0.85).toLocaleString()} ر.س</span>
        </div>
        <div class="total-row">
          <span>ضريبة القيمة المضافة (15%):</span>
          <span>${(invoice.total * 0.15).toLocaleString()} ر.س</span>
        </div>
        <div class="total-row final">
          <span>الإجمالي النهائي:</span>
          <span>${invoice.total.toLocaleString()} ر.س</span>
        </div>
      </div>

      <div class="footer">
        <p>شكراً لتعاملكم معنا</p>
        <p>هذه فاتورة مُنشأة إلكترونياً</p>
      </div>

      <script>
        window.onload = function() {
          window.print();
        }
      </script>
    </body>
    </html>
  `;

  printWindow.document.write(htmlContent);
  printWindow.document.close();
};

// طباعة تقرير
export const printReport = (title, data, columns) => {
  const printWindow = window.open('', '_blank');
  const currentDate = new Date().toLocaleDateString('ar-SA');
  
  const htmlContent = `
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      <style>
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          margin: 0;
          padding: 20px;
          background: white;
          color: #333;
          direction: rtl;
        }
        .report-header {
          text-align: center;
          border-bottom: 3px solid #005B48;
          padding-bottom: 20px;
          margin-bottom: 30px;
        }
        .company-name {
          font-size: 24px;
          font-weight: bold;
          color: #005B48;
          margin-bottom: 10px;
        }
        .report-title {
          font-size: 20px;
          color: #666;
          margin-bottom: 5px;
        }
        .report-date {
          color: #999;
          font-size: 14px;
        }
        .data-table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
        }
        .data-table th,
        .data-table td {
          border: 1px solid #ddd;
          padding: 10px;
          text-align: center;
        }
        .data-table th {
          background-color: #005B48;
          color: white;
          font-weight: bold;
        }
        .data-table tr:nth-child(even) {
          background-color: #f9f9f9;
        }
        @media print {
          body { margin: 0; }
        }
      </style>
    </head>
    <body>
      <div class="report-header">
        <div class="company-name">شركة النور للتجارة</div>
        <div class="report-title">${title}</div>
        <div class="report-date">تاريخ التقرير: ${currentDate}</div>
      </div>

      <table class="data-table">
        <thead>
          <tr>
            ${columns.map(col => `<th>${col.label}</th>`).join('')}
          </tr>
        </thead>
        <tbody>
          ${data.map(row => `
            <tr>
              ${columns.map(col => `<td>${row[col.key] || '-'}</td>`).join('')}
            </tr>
          `).join('')}
        </tbody>
      </table>

      <script>
        window.onload = function() {
          window.print();
        }
      </script>
    </body>
    </html>
  `;

  printWindow.document.write(htmlContent);
  printWindow.document.close();
};
