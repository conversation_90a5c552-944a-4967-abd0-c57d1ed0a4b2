import {
  defaultTheme_default,
  identifier_default,
  useTheme_default
} from "./chunk-EZAJAGWX.js";
import {
  __toESM,
  require_react
} from "./chunk-VCDLJVZS.js";

// node_modules/@mui/material/esm/styles/useTheme.js
var React = __toESM(require_react(), 1);
function useTheme() {
  const theme = useTheme_default(defaultTheme_default);
  if (true) {
    React.useDebugValue(theme);
  }
  return theme[identifier_default] || theme;
}

export {
  useTheme
};
//# sourceMappingURL=chunk-SVLO4HLC.js.map
