import { createTheme } from '@mui/material/styles';

// نظام الألوان الكلاسيكي البسيط
const colors = {
  primary: {
    50: '#F8F9FA',
    100: '#F1F3F4',
    200: '#E8EAED',
    300: '#DADCE0',
    400: '#BDC1C6',
    500: '#5F6368', // رمادي أساسي
    600: '#3C4043',
    700: '#202124',
    800: '#1A1A1A',
    900: '#000000',
    main: '#5F6368',
    dark: '#3C4043',
    light: '#BDC1C6',
    contrastText: '#ffffff'
  },
  secondary: {
    50: '#F8F9FA',
    100: '#F1F3F4',
    200: '#E8EAED',
    300: '#DADCE0',
    400: '#BDC1C6',
    500: '#9AA0A6',
    600: '#80868B',
    700: '#5F6368',
    800: '#3C4043',
    900: '#202124',
    main: '#9AA0A6',
    dark: '#5F6368',
    light: '#DADCE0',
    contrastText: '#ffffff'
  },
  accent: {
    blue: '#1976D2',
    green: '#388E3C',
    orange: '#F57C00',
    red: '#D32F2F',
    purple: '#7B1FA2'
  },
  neutral: {
    50: '#FFFFFF',
    100: '#FAFAFA',
    200: '#F5F5F5',
    300: '#F0F0F0',
    400: '#E0E0E0',
    500: '#BDBDBD',
    600: '#9E9E9E',
    700: '#757575',
    800: '#424242',
    900: '#212121'
  },
  background: {
    default: '#FAFAFA',
    paper: '#FFFFFF',
    light: '#F8F9FA',
    dark: '#F5F5F5'
  },
  text: {
    primary: '#202124',
    secondary: '#5F6368',
    disabled: '#9AA0A6',
    hint: '#BDC1C6'
  },
  divider: '#E8EAED',
  border: '#DADCE0',
  status: {
    success: '#388E3C',
    warning: '#F57C00',
    error: '#D32F2F',
    info: '#1976D2'
  }
};

// إعدادات الطباعة البسيطة
const typography = {
  fontFamily: [
    'Inter',
    'Roboto',
    '-apple-system',
    'BlinkMacSystemFont',
    '"Segoe UI"',
    'Arial',
    'sans-serif'
  ].join(','),
  h1: {
    fontSize: '2rem',
    fontWeight: 600,
    lineHeight: 1.3,
    color: colors.text.primary
  },
  h2: {
    fontSize: '1.75rem',
    fontWeight: 600,
    lineHeight: 1.3,
    color: colors.text.primary
  },
  h3: {
    fontSize: '1.5rem',
    fontWeight: 600,
    lineHeight: 1.4,
    color: colors.text.primary
  },
  h4: {
    fontSize: '1.25rem',
    fontWeight: 500,
    lineHeight: 1.4,
    color: colors.text.primary
  },
  h5: {
    fontSize: '1.125rem',
    fontWeight: 500,
    lineHeight: 1.5,
    color: colors.text.primary
  },
  h6: {
    fontSize: '1rem',
    fontWeight: 500,
    lineHeight: 1.5,
    color: colors.text.primary
  },
  body1: {
    fontSize: '0.875rem',
    fontWeight: 400,
    lineHeight: 1.5,
    color: colors.text.primary
  },
  body2: {
    fontSize: '0.8rem',
    fontWeight: 400,
    lineHeight: 1.4,
    color: colors.text.secondary
  },
  button: {
    fontSize: '0.875rem',
    fontWeight: 500,
    lineHeight: 1.5,
    textTransform: 'none'
  }
};

// ظلال بسيطة وهادئة
const shadows = [
  'none',
  '0px 1px 3px rgba(0, 0, 0, 0.08)',
  '0px 2px 6px rgba(0, 0, 0, 0.08)',
  '0px 4px 12px rgba(0, 0, 0, 0.08)',
  '0px 6px 16px rgba(0, 0, 0, 0.08)',
  '0px 8px 24px rgba(0, 0, 0, 0.08)',
  ...Array(19).fill('0px 8px 24px rgba(0, 0, 0, 0.08)')
];

// حدود منحنية بسيطة
const borderRadius = {
  xs: 2,
  sm: 4,
  md: 6,
  lg: 8,
  xl: 12
};

// إنشاء الثيم البسيط
const theme = createTheme({
  direction: 'rtl',
  palette: {
    mode: 'light',
    primary: colors.primary,
    secondary: colors.secondary,
    background: colors.background,
    text: colors.text,
    error: { main: colors.status.error },
    warning: { main: colors.status.warning },
    info: { main: colors.status.info },
    success: { main: colors.status.success },
    grey: colors.neutral,
    divider: colors.divider
  },
  typography,
  shadows,
  shape: {
    borderRadius: borderRadius.sm
  },
  spacing: 8,
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          fontFamily: typography.fontFamily,
          backgroundColor: colors.background.default,
          color: colors.text.primary,
          minHeight: '100vh'
        }
      }
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundColor: colors.background.paper,
          borderRadius: borderRadius.sm,
          border: `1px solid ${colors.border}`,
          boxShadow: shadows[1]
        }
      }
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: borderRadius.sm,
          textTransform: 'none',
          fontWeight: 500,
          padding: '8px 16px',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: shadows[1]
          }
        },
        contained: {
          backgroundColor: colors.primary.main,
          color: colors.primary.contrastText,
          '&:hover': {
            backgroundColor: colors.primary.dark
          }
        },
        outlined: {
          borderColor: colors.border,
          color: colors.text.primary,
          '&:hover': {
            borderColor: colors.primary.main,
            backgroundColor: colors.background.light
          }
        }
      }
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: borderRadius.sm,
            backgroundColor: colors.background.paper,
            '& fieldset': {
              borderColor: colors.border
            },
            '&:hover fieldset': {
              borderColor: colors.primary.main
            },
            '&.Mui-focused fieldset': {
              borderColor: colors.primary.main,
              borderWidth: 1
            }
          }
        }
      }
    },
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundColor: colors.background.paper,
          borderRadius: borderRadius.sm,
          border: `1px solid ${colors.border}`,
          boxShadow: shadows[1],
          '&:hover': {
            boxShadow: shadows[2]
          },
          transition: 'box-shadow 0.2s ease'
        }
      }
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          '& .MuiTableCell-head': {
            backgroundColor: colors.background.dark,
            color: colors.text.primary,
            fontWeight: 600,
            borderBottom: `1px solid ${colors.border}`
          }
        }
      }
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:nth-of-type(even)': {
            backgroundColor: colors.background.light
          },
          '&:hover': {
            backgroundColor: colors.neutral[200]
          },
          transition: 'background-color 0.2s ease'
        }
      }
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: `1px solid ${colors.border}`,
          padding: '12px 16px'
        }
      }
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: borderRadius.xs,
          fontWeight: 500
        }
      }
    },
    MuiIconButton: {
      styleOverrides: {
        root: {
          borderRadius: borderRadius.sm,
          '&:hover': {
            backgroundColor: colors.background.light
          }
        }
      }
    }
  }
});

export { theme, colors, borderRadius, shadows };
export default theme;
