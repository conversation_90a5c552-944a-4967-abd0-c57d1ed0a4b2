import { createTheme } from '@mui/material/styles';

// نظام الألوان الاحترافي
const colors = {
  primary: {
    50: '#E8F5E8',
    100: '#C8E6C9',
    200: '#A5D6A7',
    300: '#81C784',
    400: '#66BB6A',
    500: '#4CAF50', // اللون الأساسي
    600: '#43A047',
    700: '#388E3C',
    800: '#2E7D32',
    900: '#1B5E20',
    main: '#4CAF50',
    dark: '#2E7D32',
    light: '#81C784',
    contrastText: '#ffffff'
  },
  secondary: {
    50: '#E3F2FD',
    100: '#BBDEFB',
    200: '#90CAF9',
    300: '#64B5F6',
    400: '#42A5F5',
    500: '#2196F3',
    600: '#1E88E5',
    700: '#1976D2',
    800: '#1565C0',
    900: '#0D47A1',
    main: '#2196F3',
    dark: '#1565C0',
    light: '#64B5F6',
    contrastText: '#ffffff'
  },
  accent: {
    gold: '#FFD700',
    orange: '#FF9800',
    purple: '#9C27B0',
    teal: '#009688',
    indigo: '#3F51B5'
  },
  neutral: {
    50: '#FAFAFA',
    100: '#F5F5F5',
    200: '#EEEEEE',
    300: '#E0E0E0',
    400: '#BDBDBD',
    500: '#9E9E9E',
    600: '#757575',
    700: '#616161',
    800: '#424242',
    900: '#212121'
  },
  background: {
    default: '#FAFAFA',
    paper: '#FFFFFF',
    gradient: 'linear-gradient(135deg, #E8F5E8 0%, #C8E6C9 100%)',
    darkGradient: 'linear-gradient(135deg, #2E7D32 0%, #4CAF50 100%)'
  },
  text: {
    primary: '#212121',
    secondary: '#757575',
    disabled: '#BDBDBD'
  },
  status: {
    success: '#4CAF50',
    warning: '#FF9800',
    error: '#F44336',
    info: '#2196F3'
  }
};

// إعدادات الطباعة
const typography = {
  fontFamily: [
    'Cairo',
    'Roboto',
    '-apple-system',
    'BlinkMacSystemFont',
    '"Segoe UI"',
    '"Helvetica Neue"',
    'Arial',
    'sans-serif'
  ].join(','),
  h1: {
    fontSize: '2.5rem',
    fontWeight: 700,
    lineHeight: 1.2,
    letterSpacing: '-0.01562em'
  },
  h2: {
    fontSize: '2rem',
    fontWeight: 600,
    lineHeight: 1.3,
    letterSpacing: '-0.00833em'
  },
  h3: {
    fontSize: '1.75rem',
    fontWeight: 600,
    lineHeight: 1.4,
    letterSpacing: '0em'
  },
  h4: {
    fontSize: '1.5rem',
    fontWeight: 600,
    lineHeight: 1.4,
    letterSpacing: '0.00735em'
  },
  h5: {
    fontSize: '1.25rem',
    fontWeight: 500,
    lineHeight: 1.5,
    letterSpacing: '0em'
  },
  h6: {
    fontSize: '1.125rem',
    fontWeight: 500,
    lineHeight: 1.6,
    letterSpacing: '0.0075em'
  },
  body1: {
    fontSize: '1rem',
    fontWeight: 400,
    lineHeight: 1.6,
    letterSpacing: '0.00938em'
  },
  body2: {
    fontSize: '0.875rem',
    fontWeight: 400,
    lineHeight: 1.5,
    letterSpacing: '0.01071em'
  },
  button: {
    fontSize: '0.875rem',
    fontWeight: 600,
    lineHeight: 1.75,
    letterSpacing: '0.02857em',
    textTransform: 'none'
  }
};

// إعدادات الظلال
const shadows = [
  'none',
  '0px 2px 4px rgba(0, 0, 0, 0.05)',
  '0px 4px 8px rgba(0, 0, 0, 0.08)',
  '0px 8px 16px rgba(0, 0, 0, 0.1)',
  '0px 12px 24px rgba(0, 0, 0, 0.12)',
  '0px 16px 32px rgba(0, 0, 0, 0.15)',
  '0px 20px 40px rgba(0, 0, 0, 0.18)',
  '0px 24px 48px rgba(0, 0, 0, 0.2)',
  '0px 32px 64px rgba(0, 0, 0, 0.25)',
  ...Array(15).fill('0px 32px 64px rgba(0, 0, 0, 0.25)')
];

// إعدادات الحدود المنحنية
const borderRadius = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 24,
  xxl: 32
};

// إنشاء الثيم
const theme = createTheme({
  direction: 'rtl',
  palette: {
    mode: 'light',
    primary: colors.primary,
    secondary: colors.secondary,
    background: colors.background,
    text: colors.text,
    error: { main: colors.status.error },
    warning: { main: colors.status.warning },
    info: { main: colors.status.info },
    success: { main: colors.status.success },
    grey: colors.neutral
  },
  typography,
  shadows,
  shape: {
    borderRadius: borderRadius.sm
  },
  spacing: 8,
  components: {
    MuiCssBaseline: {
      styleOverrides: {
        body: {
          fontFamily: typography.fontFamily,
          background: colors.background.gradient,
          minHeight: '100vh'
        }
      }
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: borderRadius.md,
          border: `1px solid ${colors.neutral[200]}`,
          boxShadow: shadows[2]
        },
        elevation1: {
          boxShadow: shadows[1]
        },
        elevation2: {
          boxShadow: shadows[2]
        },
        elevation3: {
          boxShadow: shadows[3]
        },
        elevation4: {
          boxShadow: shadows[4]
        }
      }
    },
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: borderRadius.sm,
          textTransform: 'none',
          fontWeight: 600,
          padding: '10px 24px',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: shadows[2],
            transform: 'translateY(-1px)'
          }
        },
        contained: {
          background: colors.background.darkGradient,
          '&:hover': {
            background: colors.background.darkGradient,
            filter: 'brightness(1.1)'
          }
        }
      }
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            borderRadius: borderRadius.sm,
            '& fieldset': {
              borderColor: colors.neutral[300]
            },
            '&:hover fieldset': {
              borderColor: colors.primary[500]
            },
            '&.Mui-focused fieldset': {
              borderColor: colors.primary[500],
              borderWidth: 2
            }
          }
        }
      }
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: borderRadius.md,
          border: `1px solid ${colors.neutral[200]}`,
          boxShadow: shadows[2],
          '&:hover': {
            boxShadow: shadows[4],
            transform: 'translateY(-2px)'
          },
          transition: 'all 0.3s ease'
        }
      }
    },
    MuiChip: {
      styleOverrides: {
        root: {
          borderRadius: borderRadius.xs,
          fontWeight: 500
        }
      }
    },
    MuiTableHead: {
      styleOverrides: {
        root: {
          '& .MuiTableCell-head': {
            background: colors.background.darkGradient,
            color: 'white',
            fontWeight: 600,
            borderBottom: 'none'
          }
        }
      }
    },
    MuiTableRow: {
      styleOverrides: {
        root: {
          '&:nth-of-type(even)': {
            backgroundColor: colors.neutral[50]
          },
          '&:hover': {
            backgroundColor: colors.primary[50],
            transform: 'scale(1.001)',
            boxShadow: shadows[1]
          },
          transition: 'all 0.2s ease'
        }
      }
    }
  }
});

export { theme, colors, borderRadius, shadows };
export default theme;
