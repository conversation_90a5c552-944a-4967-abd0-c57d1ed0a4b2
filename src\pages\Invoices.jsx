import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  InputAdornment,
  IconButton,
  Button,
  Chip,
  Menu,
  MenuItem,
  Grid,
  FormControl,
  InputLabel,
  Select,
  Divider
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import AddIcon from '@mui/icons-material/Add';
import FilterListIcon from '@mui/icons-material/FilterList';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import ReceiptIcon from '@mui/icons-material/Receipt';
import PrintIcon from '@mui/icons-material/Print';
import FileDownloadIcon from '@mui/icons-material/FileDownload';
import { useApp } from '../context/AppContext';
import { useNavigate } from 'react-router-dom';
import { printInvoice, exportToCSV } from '../utils/exportUtils';
import SnackbarNotification from '../components/SnackbarNotification';
import FormDialog from '../components/FormDialog';
import ConfirmDialog from '../components/ConfirmDialog';

// نموذج إضافة/تعديل فاتورة
const InvoiceForm = ({ open, onClose, invoice = null, onSave }) => {
  const { state } = useApp();
  const [invoiceData, setInvoiceData] = useState({
    number: '',
    date: new Date().toISOString().split('T')[0],
    customer: '',
    type: 'بيع',
    items: [],
    subtotal: 0,
    tax: 0,
    discount: 0,
    total: 0,
    status: 'معلقة',
    notes: ''
  });

  const [selectedProduct, setSelectedProduct] = useState('');
  const [productQuantity, setProductQuantity] = useState(1);
  const [productPrice, setProductPrice] = useState(0);

  React.useEffect(() => {
    if (invoice) {
      setInvoiceData(invoice);
    } else {
      // إنشاء رقم فاتورة تلقائي
      const invoiceNumber = `INV-${Date.now()}`;
      setInvoiceData(prev => ({
        ...prev,
        number: invoiceNumber
      }));
    }
  }, [invoice]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setInvoiceData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddItem = () => {
    if (!selectedProduct) return;

    const product = state.products.find(p => p.id === selectedProduct);
    if (!product) return;

    const newItem = {
      id: Date.now(),
      productId: product.id,
      productName: product.name,
      quantity: productQuantity,
      price: productPrice || product.salePrice,
      total: productQuantity * (productPrice || product.salePrice)
    };

    const updatedItems = [...invoiceData.items, newItem];
    const subtotal = updatedItems.reduce((sum, item) => sum + item.total, 0);
    const tax = subtotal * 0.15; // ضريبة 15%
    const total = subtotal + tax - invoiceData.discount;

    setInvoiceData(prev => ({
      ...prev,
      items: updatedItems,
      subtotal,
      tax,
      total
    }));

    // إعادة تعيين الحقول
    setSelectedProduct('');
    setProductQuantity(1);
    setProductPrice(0);
  };

  const handleRemoveItem = (itemId) => {
    const updatedItems = invoiceData.items.filter(item => item.id !== itemId);
    const subtotal = updatedItems.reduce((sum, item) => sum + item.total, 0);
    const tax = subtotal * 0.15;
    const total = subtotal + tax - invoiceData.discount;

    setInvoiceData(prev => ({
      ...prev,
      items: updatedItems,
      subtotal,
      tax,
      total
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(invoiceData);
    onClose();
  };

  return (
    <FormDialog
      open={open}
      onClose={onClose}
      onSubmit={handleSubmit}
      title={invoice ? 'تعديل الفاتورة' : 'إضافة فاتورة جديدة'}
      maxWidth="lg"
    >
      <Grid container spacing={3}>
        {/* معلومات الفاتورة الأساسية */}
        <Grid item xs={12} md={6}>
          <TextField
            name="number"
            label="رقم الفاتورة"
            value={invoiceData.number}
            onChange={handleChange}
            fullWidth
            required
            variant="outlined"
            margin="normal"
            disabled
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <TextField
            name="date"
            label="تاريخ الفاتورة"
            type="date"
            value={invoiceData.date}
            onChange={handleChange}
            fullWidth
            required
            variant="outlined"
            margin="normal"
            InputLabelProps={{ shrink: true }}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth margin="normal">
            <InputLabel>العميل/المورد</InputLabel>
            <Select
              name="customer"
              value={invoiceData.customer}
              onChange={handleChange}
              label="العميل/المورد"
              required
            >
              {invoiceData.type === 'بيع'
                ? state.customers.map(customer => (
                    <MenuItem key={customer.id} value={customer.name}>
                      {customer.name}
                    </MenuItem>
                  ))
                : state.suppliers.map(supplier => (
                    <MenuItem key={supplier.id} value={supplier.name}>
                      {supplier.name}
                    </MenuItem>
                  ))
              }
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth margin="normal">
            <InputLabel>نوع الفاتورة</InputLabel>
            <Select
              name="type"
              value={invoiceData.type}
              onChange={handleChange}
              label="نوع الفاتورة"
            >
              <MenuItem value="بيع">فاتورة بيع</MenuItem>
              <MenuItem value="شراء">فاتورة شراء</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        {/* إضافة المنتجات */}
        <Grid item xs={12}>
          <Typography variant="h6" sx={{ mb: 2, color: '#005B48' }}>
            إضافة المنتجات
          </Typography>
          <Paper sx={{ p: 2, mb: 2 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>المنتج</InputLabel>
                  <Select
                    value={selectedProduct}
                    onChange={(e) => {
                      setSelectedProduct(e.target.value);
                      const product = state.products.find(p => p.id === e.target.value);
                      if (product) {
                        setProductPrice(product.salePrice);
                      }
                    }}
                    label="المنتج"
                  >
                    {state.products.map(product => (
                      <MenuItem key={product.id} value={product.id}>
                        {product.name} - {product.salePrice} ر.س
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <TextField
                  label="الكمية"
                  type="number"
                  value={productQuantity}
                  onChange={(e) => setProductQuantity(parseInt(e.target.value) || 1)}
                  fullWidth
                  inputProps={{ min: 1 }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  label="السعر"
                  type="number"
                  value={productPrice}
                  onChange={(e) => setProductPrice(parseFloat(e.target.value) || 0)}
                  fullWidth
                  InputProps={{
                    endAdornment: <InputAdornment position="end">ر.س</InputAdornment>
                  }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <Button
                  variant="contained"
                  onClick={handleAddItem}
                  disabled={!selectedProduct}
                  fullWidth
                  sx={{ bgcolor: '#005B48', '&:hover': { bgcolor: '#004a3b' } }}
                >
                  إضافة
                </Button>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* قائمة المنتجات المضافة */}
        {invoiceData.items.length > 0 && (
          <Grid item xs={12}>
            <Typography variant="h6" sx={{ mb: 2, color: '#005B48' }}>
              المنتجات المضافة
            </Typography>
            <TableContainer component={Paper}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>المنتج</TableCell>
                    <TableCell>الكمية</TableCell>
                    <TableCell>السعر</TableCell>
                    <TableCell>الإجمالي</TableCell>
                    <TableCell>إجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {invoiceData.items.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.productName}</TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell>{item.price} ر.س</TableCell>
                      <TableCell>{item.total} ر.س</TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleRemoveItem(item.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
        )}

        {/* ملخص الفاتورة */}
        {invoiceData.items.length > 0 && (
          <Grid item xs={12} md={6} sx={{ ml: 'auto' }}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="h6" sx={{ mb: 2, color: '#005B48' }}>
                ملخص الفاتورة
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>المجموع الفرعي:</Typography>
                <Typography>{invoiceData.subtotal.toFixed(2)} ر.س</Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>الضريبة (15%):</Typography>
                <Typography>{invoiceData.tax.toFixed(2)} ر.س</Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>الخصم:</Typography>
                <TextField
                  size="small"
                  type="number"
                  value={invoiceData.discount}
                  onChange={(e) => {
                    const discount = parseFloat(e.target.value) || 0;
                    const total = invoiceData.subtotal + invoiceData.tax - discount;
                    setInvoiceData(prev => ({ ...prev, discount, total }));
                  }}
                  sx={{ width: 100 }}
                  InputProps={{
                    endAdornment: <InputAdornment position="end">ر.س</InputAdornment>
                  }}
                />
              </Box>
              <Divider sx={{ my: 1 }} />
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  الإجمالي:
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#005B48' }}>
                  {invoiceData.total.toFixed(2)} ر.س
                </Typography>
              </Box>
            </Paper>
          </Grid>
        )}

        {/* ملاحظات */}
        <Grid item xs={12}>
          <TextField
            name="notes"
            label="ملاحظات"
            value={invoiceData.notes}
            onChange={handleChange}
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            margin="normal"
          />
        </Grid>
      </Grid>
    </FormDialog>
  );
};

const Invoices = () => {
  const { state, addInvoice, updateInvoice, deleteInvoice } = useApp();
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [editingInvoice, setEditingInvoice] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState({ open: false, invoiceId: null });

  const handleFilterClick = (event) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  // وظائف إدارة الفواتير
  const handleSaveInvoice = (invoiceData) => {
    if (editingInvoice) {
      updateInvoice({ ...invoiceData, id: editingInvoice.id });
      setNotification({ open: true, message: 'تم تحديث الفاتورة بنجاح', severity: 'success' });
    } else {
      addInvoice(invoiceData);
      setNotification({ open: true, message: 'تم إضافة الفاتورة بنجاح', severity: 'success' });
    }
    setEditingInvoice(null);
  };

  const handleEditInvoice = (invoice) => {
    setEditingInvoice(invoice);
    setOpenAddDialog(true);
  };

  const handleDeleteInvoice = (invoiceId) => {
    setDeleteConfirm({ open: true, invoiceId });
  };

  const confirmDelete = () => {
    deleteInvoice(deleteConfirm.invoiceId);
    setDeleteConfirm({ open: false, invoiceId: null });
    setNotification({ open: true, message: 'تم حذف الفاتورة بنجاح', severity: 'success' });
  };

  const handleCloseDialog = () => {
    setOpenAddDialog(false);
    setEditingInvoice(null);
  };

  // وظائف الطباعة والتصدير
  const handlePrintInvoice = (invoice) => {
    printInvoice(invoice);
    setNotification({ open: true, message: 'تم إرسال الفاتورة للطباعة', severity: 'success' });
  };

  const handleExportInvoices = () => {
    const data = state.invoices.map(invoice => ({
      'رقم الفاتورة': invoice.number,
      'التاريخ': invoice.date,
      'العميل/المورد': invoice.customer,
      'النوع': invoice.type,
      'المبلغ': invoice.total,
      'الحالة': invoice.status
    }));
    exportToCSV(data, 'الفواتير');
    setNotification({ open: true, message: 'تم تصدير الفواتير بنجاح', severity: 'success' });
  };

  // حساب الإحصائيات
  const salesInvoices = state.invoices.filter(inv => inv.type === 'بيع');
  const purchaseInvoices = state.invoices.filter(inv => inv.type === 'شراء');
  const pendingInvoices = state.invoices.filter(inv => inv.status === 'معلقة');

  const totalSales = salesInvoices.reduce((sum, inv) => sum + inv.total, 0);
  const totalPurchases = purchaseInvoices.reduce((sum, inv) => sum + inv.total, 0);

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>الفواتير</Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<FileDownloadIcon />}
            onClick={handleExportInvoices}
            sx={{
              borderColor: '#e0e0e0',
              color: '#424242',
              '&:hover': {
                borderColor: '#bdbdbd',
                bgcolor: '#f5f7fa',
              }
            }}
          >
            تصدير
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/create-invoice')}
            sx={{
              bgcolor: '#005B48',
              '&:hover': { bgcolor: '#004a3b' }
            }}
          >
            فاتورة جديدة
          </Button>
        </Box>
      </Box>

      {/* شريط البحث والفلترة */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          mb: 3,
          borderRadius: 3
        }}
      >
        <Box sx={{ display: 'flex', gap: 2 }}>
          <TextField
            placeholder="بحث في الفواتير..."
            variant="outlined"
            size="small"
            fullWidth
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: '#9e9e9e' }} />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                bgcolor: '#f5f7fa',
                '&:hover fieldset': {
                  borderColor: '#bdbdbd',
                }
              }
            }}
          />
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={handleFilterClick}
            sx={{
              minWidth: 130,
              borderColor: '#e0e0e0',
              color: '#424242',
              '&:hover': {
                borderColor: '#bdbdbd',
                bgcolor: '#f5f7fa',
              }
            }}
          >
            تصفية
          </Button>
        </Box>
      </Paper>

      {/* إحصائيات سريعة */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Paper
          elevation={0}
          sx={{
            flex: 1,
            p: 2,
            borderRadius: 3,
            bgcolor: '#e8f5e9'
          }}
        >
          <Typography variant="h6" sx={{ color: '#2e7d32', mb: 1 }}>إجمالي المبيعات</Typography>
          <Typography variant="h4" sx={{ color: '#1b5e20', fontWeight: 'bold' }}>{totalSales.toLocaleString()} ر.س</Typography>
        </Paper>
        <Paper
          elevation={0}
          sx={{
            flex: 1,
            p: 2,
            borderRadius: 3,
            bgcolor: '#e3f2fd'
          }}
        >
          <Typography variant="h6" sx={{ color: '#1976d2', mb: 1 }}>إجمالي المشتريات</Typography>
          <Typography variant="h4" sx={{ color: '#0d47a1', fontWeight: 'bold' }}>{totalPurchases.toLocaleString()} ر.س</Typography>
        </Paper>
        <Paper
          elevation={0}
          sx={{
            flex: 1,
            p: 2,
            borderRadius: 3,
            bgcolor: '#fff3e0'
          }}
        >
          <Typography variant="h6" sx={{ color: '#f57c00', mb: 1 }}>الفواتير المعلقة</Typography>
          <Typography variant="h4" sx={{ color: '#e65100', fontWeight: 'bold' }}>{pendingInvoices.length}</Typography>
        </Paper>
      </Box>

      {/* جدول الفواتير */}
      <TableContainer
        component={Paper}
        elevation={0}
        sx={{ borderRadius: 3 }}
      >
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>#</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>رقم الفاتورة</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>التاريخ</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>العميل/المورد</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>النوع</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الحالة</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>إجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {state.invoices
              .filter(invoice =>
                invoice.number.toLowerCase().includes(searchQuery.toLowerCase()) ||
                invoice.customer.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .map((invoice) => (
              <TableRow
                key={invoice.id}
                sx={{ '&:hover': { bgcolor: '#f5f7fa' } }}
              >
                <TableCell>{invoice.id}</TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ReceiptIcon sx={{ color: '#757575', fontSize: 20 }} />
                    {invoice.number}
                  </Box>
                </TableCell>
                <TableCell>{invoice.date}</TableCell>
                <TableCell>{invoice.customer}</TableCell>
                <TableCell>
                  <Chip
                    label={invoice.type}
                    size="small"
                    sx={{
                      bgcolor: invoice.type === 'بيع' ? '#e3f2fd' : '#f3e5f5',
                      color: invoice.type === 'بيع' ? '#1976d2' : '#9c27b0',
                    }}
                  />
                </TableCell>
                <TableCell>{invoice.total} ر.س</TableCell>
                <TableCell>
                  <Chip
                    label={invoice.status}
                    size="small"
                    sx={{
                      bgcolor: invoice.status === 'مدفوعة' ? '#e8f5e9' : '#fff3e0',
                      color: invoice.status === 'مدفوعة' ? '#2e7d32' : '#f57c00',
                    }}
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton
                      size="small"
                      onClick={() => handlePrintInvoice(invoice)}
                      title="طباعة الفاتورة"
                      sx={{
                        color: '#1976d2',
                        '&:hover': { bgcolor: '#e3f2fd' }
                      }}
                    >
                      <PrintIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleEditInvoice(invoice)}
                      title="تعديل الفاتورة"
                      sx={{
                        color: '#2e7d32',
                        '&:hover': { bgcolor: '#e8f5e9' }
                      }}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDeleteInvoice(invoice.id)}
                      title="حذف الفاتورة"
                      sx={{
                        color: '#d32f2f',
                        '&:hover': { bgcolor: '#ffebee' }
                      }}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* قائمة الفلترة */}
      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={handleFilterClose}
      >
        <MenuItem onClick={handleFilterClose}>فواتير البيع</MenuItem>
        <MenuItem onClick={handleFilterClose}>فواتير الشراء</MenuItem>
        <MenuItem onClick={handleFilterClose}>الفواتير المدفوعة</MenuItem>
        <MenuItem onClick={handleFilterClose}>الفواتير المعلقة</MenuItem>
      </Menu>

      {/* نافذة إضافة/تعديل فاتورة */}
      <InvoiceForm
        open={openAddDialog}
        onClose={handleCloseDialog}
        invoice={editingInvoice}
        onSave={handleSaveInvoice}
      />

      {/* حوار تأكيد الحذف */}
      <ConfirmDialog
        open={deleteConfirm.open}
        onClose={() => setDeleteConfirm({ open: false, invoiceId: null })}
        onConfirm={confirmDelete}
        title="حذف الفاتورة"
        message="هل أنت متأكد من أنك تريد حذف هذه الفاتورة؟ لا يمكن التراجع عن هذا الإجراء."
      />

      {/* إشعارات */}
      <SnackbarNotification
        open={notification.open}
        onClose={() => setNotification({ ...notification, open: false })}
        message={notification.message}
        severity={notification.severity}
      />
    </Box>
  );
};

export default Invoices;
