import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  InputAdornment,
  IconButton,
  Button,
  Chip,
  Menu,
  MenuItem,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import AddIcon from '@mui/icons-material/Add';
import FilterListIcon from '@mui/icons-material/FilterList';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import ReceiptIcon from '@mui/icons-material/Receipt';
import PrintIcon from '@mui/icons-material/Print';
import FileDownloadIcon from '@mui/icons-material/FileDownload';

// بيانات تجريبية للفواتير
const sampleInvoices = [
  {
    id: 1,
    number: 'INV-001',
    date: '2025-05-26',
    customer: 'شركة النور للتجارة',
    total: 15000,
    status: 'مدفوعة',
    type: 'بيع'
  },
  {
    id: 2,
    number: 'INV-002',
    date: '2025-05-25',
    customer: 'مؤسسة الأمل',
    total: 8500,
    status: 'معلقة',
    type: 'شراء'
  },
];

const Invoices = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);

  const handleFilterClick = (event) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>الفواتير</Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button 
            variant="outlined"
            startIcon={<FileDownloadIcon />}
            sx={{ 
              borderColor: '#e0e0e0',
              color: '#424242',
              '&:hover': {
                borderColor: '#bdbdbd',
                bgcolor: '#f5f7fa',
              }
            }}
          >
            تصدير
          </Button>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            sx={{ 
              bgcolor: '#005B48',
              '&:hover': { bgcolor: '#004a3b' }
            }}
          >
            فاتورة جديدة
          </Button>
        </Box>
      </Box>

      {/* شريط البحث والفلترة */}
      <Paper 
        elevation={0}
        sx={{ 
          p: 2, 
          mb: 3,
          borderRadius: 3
        }}
      >
        <Box sx={{ display: 'flex', gap: 2 }}>
          <TextField
            placeholder="بحث في الفواتير..."
            variant="outlined"
            size="small"
            fullWidth
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: '#9e9e9e' }} />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                bgcolor: '#f5f7fa',
                '&:hover fieldset': {
                  borderColor: '#bdbdbd',
                }
              }
            }}
          />
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={handleFilterClick}
            sx={{ 
              minWidth: 130,
              borderColor: '#e0e0e0',
              color: '#424242',
              '&:hover': {
                borderColor: '#bdbdbd',
                bgcolor: '#f5f7fa',
              }
            }}
          >
            تصفية
          </Button>
        </Box>
      </Paper>

      {/* إحصائيات سريعة */}
      <Box sx={{ display: 'flex', gap: 2, mb: 3 }}>
        <Paper 
          elevation={0} 
          sx={{ 
            flex: 1, 
            p: 2, 
            borderRadius: 3,
            bgcolor: '#e8f5e9'
          }}
        >
          <Typography variant="h6" sx={{ color: '#2e7d32', mb: 1 }}>إجمالي المبيعات</Typography>
          <Typography variant="h4" sx={{ color: '#1b5e20', fontWeight: 'bold' }}>15,000 ر.س</Typography>
        </Paper>
        <Paper 
          elevation={0} 
          sx={{ 
            flex: 1, 
            p: 2, 
            borderRadius: 3,
            bgcolor: '#e3f2fd'
          }}
        >
          <Typography variant="h6" sx={{ color: '#1976d2', mb: 1 }}>إجمالي المشتريات</Typography>
          <Typography variant="h4" sx={{ color: '#0d47a1', fontWeight: 'bold' }}>8,500 ر.س</Typography>
        </Paper>
        <Paper 
          elevation={0} 
          sx={{ 
            flex: 1, 
            p: 2, 
            borderRadius: 3,
            bgcolor: '#fff3e0'
          }}
        >
          <Typography variant="h6" sx={{ color: '#f57c00', mb: 1 }}>الفواتير المعلقة</Typography>
          <Typography variant="h4" sx={{ color: '#e65100', fontWeight: 'bold' }}>1</Typography>
        </Paper>
      </Box>

      {/* جدول الفواتير */}
      <TableContainer 
        component={Paper} 
        elevation={0}
        sx={{ borderRadius: 3 }}
      >
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>#</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>رقم الفاتورة</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>التاريخ</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>العميل/المورد</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>النوع</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>المبلغ</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الحالة</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>إجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {sampleInvoices.map((invoice) => (
              <TableRow 
                key={invoice.id}
                sx={{ '&:hover': { bgcolor: '#f5f7fa' } }}
              >
                <TableCell>{invoice.id}</TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <ReceiptIcon sx={{ color: '#757575', fontSize: 20 }} />
                    {invoice.number}
                  </Box>
                </TableCell>
                <TableCell>{invoice.date}</TableCell>
                <TableCell>{invoice.customer}</TableCell>
                <TableCell>
                  <Chip 
                    label={invoice.type}
                    size="small"
                    sx={{ 
                      bgcolor: invoice.type === 'بيع' ? '#e3f2fd' : '#f3e5f5',
                      color: invoice.type === 'بيع' ? '#1976d2' : '#9c27b0',
                    }}
                  />
                </TableCell>
                <TableCell>{invoice.total} ر.س</TableCell>
                <TableCell>
                  <Chip 
                    label={invoice.status}
                    size="small"
                    sx={{ 
                      bgcolor: invoice.status === 'مدفوعة' ? '#e8f5e9' : '#fff3e0',
                      color: invoice.status === 'مدفوعة' ? '#2e7d32' : '#f57c00',
                    }}
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton 
                      size="small"
                      sx={{ 
                        color: '#1976d2',
                        '&:hover': { bgcolor: '#e3f2fd' }
                      }}
                    >
                      <PrintIcon fontSize="small" />
                    </IconButton>
                    <IconButton 
                      size="small"
                      sx={{ 
                        color: '#757575',
                        '&:hover': { bgcolor: '#f5f5f5' }
                      }}
                    >
                      <MoreVertIcon fontSize="small" />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* قائمة الفلترة */}
      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={handleFilterClose}
      >
        <MenuItem onClick={handleFilterClose}>فواتير البيع</MenuItem>
        <MenuItem onClick={handleFilterClose}>فواتير الشراء</MenuItem>
        <MenuItem onClick={handleFilterClose}>الفواتير المدفوعة</MenuItem>
        <MenuItem onClick={handleFilterClose}>الفواتير المعلقة</MenuItem>
      </Menu>
    </Box>
  );
};

export default Invoices;
