import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  TextField,
  InputAdornment,
  Chip
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import InventoryIcon from '@mui/icons-material/Inventory';
import StorageIcon from '@mui/icons-material/Storage';

// بيانات تجريبية للمخازن
const warehousesData = [
  { 
    id: 1, 
    name: 'المخزن الرئيسي', 
    location: 'المنطقة الصناعية', 
    capacity: '1000',
    used: '650',
    status: 'نشط'
  },
  { 
    id: 2, 
    name: 'مخزن الفرع الأول', 
    location: 'وسط المدينة', 
    capacity: '500',
    used: '320',
    status: 'نشط'
  },
  { 
    id: 3, 
    name: 'مخزن البضائع الجاهزة', 
    location: 'المنطقة الحرة', 
    capacity: '800',
    used: '540',
    status: 'صيانة'
  },
];

function Warehouses() {
  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" sx={{ color: '#005B48', fontWeight: 'bold' }}>
          المخازن
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          sx={{
            backgroundColor: '#005B48',
            '&:hover': { backgroundColor: '#00483A' }
          }}
        >
          إضافة مخزن جديد
        </Button>
      </Box>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#005B48', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <WarehouseIcon fontSize="large" />
              <Box>
                <Typography variant="h6">إجمالي المخازن</Typography>
                <Typography variant="h4">{warehousesData.length}</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#00796B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <StorageIcon fontSize="large" />
              <Box>
                <Typography variant="h6">السعة الإجمالية</Typography>
                <Typography variant="h4">2300</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#00897B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <InventoryIcon fontSize="large" />
              <Box>
                <Typography variant="h6">المساحة المستخدمة</Typography>
                <Typography variant="h4">1510</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* شريط البحث */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="بحث في المخازن..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              '&:hover fieldset': {
                borderColor: '#005B48',
              },
            },
          }}
        />
      </Box>

      {/* جدول المخازن */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell>اسم المخزن</TableCell>
              <TableCell>الموقع</TableCell>
              <TableCell>السعة الكلية</TableCell>
              <TableCell>المساحة المستخدمة</TableCell>
              <TableCell>الحالة</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {warehousesData.map((warehouse) => (
              <TableRow key={warehouse.id}>
                <TableCell>{warehouse.name}</TableCell>
                <TableCell>{warehouse.location}</TableCell>
                <TableCell>{warehouse.capacity}</TableCell>
                <TableCell>{warehouse.used}</TableCell>
                <TableCell>
                  <Chip
                    label={warehouse.status}
                    color={warehouse.status === 'نشط' ? 'success' : 'warning'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton size="small" color="primary">
                      <EditIcon />
                    </IconButton>
                    <IconButton size="small" color="error">
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}

export default Warehouses;
