import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  TextField,
  InputAdornment,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import WarehouseIcon from '@mui/icons-material/Warehouse';
import InventoryIcon from '@mui/icons-material/Inventory';
import StorageIcon from '@mui/icons-material/Storage';
import { useApp } from '../context/AppContext';
import FormDialog from '../components/FormDialog';
import ConfirmDialog from '../components/ConfirmDialog';
import SnackbarNotification from '../components/SnackbarNotification';

// نموذج إضافة/تعديل مخزن
const WarehouseForm = ({ open, onClose, warehouse = null, onSave }) => {
  const [warehouseData, setWarehouseData] = useState({
    name: '',
    location: '',
    address: '',
    manager: '',
    phone: '',
    email: '',
    capacity: 0,
    currentUsage: 0,
    type: 'رئيسي',
    status: 'نشط',
    notes: ''
  });

  React.useEffect(() => {
    if (warehouse) {
      setWarehouseData(warehouse);
    } else {
      setWarehouseData({
        name: '',
        location: '',
        address: '',
        manager: '',
        phone: '',
        email: '',
        capacity: 0,
        currentUsage: 0,
        type: 'رئيسي',
        status: 'نشط',
        notes: ''
      });
    }
  }, [warehouse]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setWarehouseData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(warehouseData);
    onClose();
  };

  return (
    <FormDialog
      open={open}
      onClose={onClose}
      onSubmit={handleSubmit}
      title={warehouse ? 'تعديل المخزن' : 'إضافة مخزن جديد'}
      maxWidth="md"
    >
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6}>
          <TextField
            name="name"
            label="اسم المخزن"
            value={warehouseData.name}
            onChange={handleChange}
            fullWidth
            required
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            name="location"
            label="الموقع"
            value={warehouseData.location}
            onChange={handleChange}
            fullWidth
            required
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12}>
          <TextField
            name="address"
            label="العنوان التفصيلي"
            value={warehouseData.address}
            onChange={handleChange}
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            name="manager"
            label="مدير المخزن"
            value={warehouseData.manager}
            onChange={handleChange}
            fullWidth
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            name="phone"
            label="رقم الهاتف"
            value={warehouseData.phone}
            onChange={handleChange}
            fullWidth
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            name="email"
            label="البريد الإلكتروني"
            type="email"
            value={warehouseData.email}
            onChange={handleChange}
            fullWidth
            variant="outlined"
            margin="normal"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth margin="normal">
            <InputLabel>نوع المخزن</InputLabel>
            <Select
              name="type"
              value={warehouseData.type}
              onChange={handleChange}
              label="نوع المخزن"
            >
              <MenuItem value="رئيسي">رئيسي</MenuItem>
              <MenuItem value="فرعي">فرعي</MenuItem>
              <MenuItem value="مؤقت">مؤقت</MenuItem>
              <MenuItem value="تخزين بارد">تخزين بارد</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            name="capacity"
            label="السعة الإجمالية"
            type="number"
            value={warehouseData.capacity}
            onChange={handleChange}
            fullWidth
            variant="outlined"
            margin="normal"
            InputProps={{
              endAdornment: <InputAdornment position="end">متر مكعب</InputAdornment>,
            }}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <TextField
            name="currentUsage"
            label="الاستخدام الحالي"
            type="number"
            value={warehouseData.currentUsage}
            onChange={handleChange}
            fullWidth
            variant="outlined"
            margin="normal"
            InputProps={{
              endAdornment: <InputAdornment position="end">متر مكعب</InputAdornment>,
            }}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth margin="normal">
            <InputLabel>حالة المخزن</InputLabel>
            <Select
              name="status"
              value={warehouseData.status}
              onChange={handleChange}
              label="حالة المخزن"
            >
              <MenuItem value="نشط">نشط</MenuItem>
              <MenuItem value="غير نشط">غير نشط</MenuItem>
              <MenuItem value="صيانة">صيانة</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12}>
          <TextField
            name="notes"
            label="ملاحظات"
            value={warehouseData.notes}
            onChange={handleChange}
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            margin="normal"
          />
        </Grid>
      </Grid>
    </FormDialog>
  );
};

function Warehouses() {
  const { state, addWarehouse, updateWarehouse, deleteWarehouse } = useApp();
  const [searchQuery, setSearchQuery] = useState('');
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [editingWarehouse, setEditingWarehouse] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState({ open: false, warehouseId: null });
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });

  // وظائف إدارة المخازن
  const handleSaveWarehouse = (warehouseData) => {
    if (editingWarehouse) {
      updateWarehouse({ ...warehouseData, id: editingWarehouse.id });
      setNotification({ open: true, message: 'تم تحديث المخزن بنجاح', severity: 'success' });
    } else {
      addWarehouse(warehouseData);
      setNotification({ open: true, message: 'تم إضافة المخزن بنجاح', severity: 'success' });
    }
    setEditingWarehouse(null);
  };

  const handleEditWarehouse = (warehouse) => {
    setEditingWarehouse(warehouse);
    setOpenAddDialog(true);
  };

  const handleDeleteWarehouse = (warehouseId) => {
    setDeleteConfirm({ open: true, warehouseId });
  };

  const confirmDelete = () => {
    deleteWarehouse(deleteConfirm.warehouseId);
    setDeleteConfirm({ open: false, warehouseId: null });
    setNotification({ open: true, message: 'تم حذف المخزن بنجاح', severity: 'success' });
  };

  const handleCloseDialog = () => {
    setOpenAddDialog(false);
    setEditingWarehouse(null);
  };

  // حساب الإحصائيات
  const totalWarehouses = state.warehouses.length;
  const totalCapacity = state.warehouses.reduce((sum, warehouse) => sum + warehouse.capacity, 0);
  const totalUsed = state.warehouses.reduce((sum, warehouse) => sum + warehouse.used, 0);
  const utilizationRate = totalCapacity > 0 ? (totalUsed / totalCapacity) * 100 : 0;
  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" sx={{ color: '#005B48', fontWeight: 'bold' }}>
          المخازن
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpenAddDialog(true)}
          sx={{
            backgroundColor: '#005B48',
            '&:hover': { backgroundColor: '#00483A' }
          }}
        >
          إضافة مخزن جديد
        </Button>
      </Box>

      {/* بطاقات الإحصائيات */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#005B48', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <WarehouseIcon fontSize="large" />
              <Box>
                <Typography variant="h6">إجمالي المخازن</Typography>
                <Typography variant="h4">{totalWarehouses}</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#00796B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <StorageIcon fontSize="large" />
              <Box>
                <Typography variant="h6">السعة الإجمالية</Typography>
                <Typography variant="h4">{totalCapacity}</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#00897B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <InventoryIcon fontSize="large" />
              <Box>
                <Typography variant="h6">المساحة المستخدمة</Typography>
                <Typography variant="h4">{totalUsed}</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* شريط البحث */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="بحث في المخازن..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              '&:hover fieldset': {
                borderColor: '#005B48',
              },
            },
          }}
        />
      </Box>

      {/* جدول المخازن */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell>اسم المخزن</TableCell>
              <TableCell>الموقع</TableCell>
              <TableCell>السعة الكلية</TableCell>
              <TableCell>المساحة المستخدمة</TableCell>
              <TableCell>الحالة</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {state.warehouses
              .filter(warehouse =>
                warehouse.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                warehouse.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
                warehouse.manager?.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .map((warehouse) => (
              <TableRow key={warehouse.id}>
                <TableCell>{warehouse.name}</TableCell>
                <TableCell>{warehouse.location}</TableCell>
                <TableCell>{warehouse.capacity}</TableCell>
                <TableCell>{warehouse.used}</TableCell>
                <TableCell>
                  <Chip
                    label={warehouse.status}
                    color={warehouse.status === 'نشط' ? 'success' : 'warning'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleEditWarehouse(warehouse)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteWarehouse(warehouse.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* نافذة إضافة/تعديل مخزن */}
      <WarehouseForm
        open={openAddDialog}
        onClose={handleCloseDialog}
        warehouse={editingWarehouse}
        onSave={handleSaveWarehouse}
      />

      {/* حوار تأكيد الحذف */}
      <ConfirmDialog
        open={deleteConfirm.open}
        onClose={() => setDeleteConfirm({ open: false, warehouseId: null })}
        onConfirm={confirmDelete}
        title="حذف المخزن"
        message="هل أنت متأكد من أنك تريد حذف هذا المخزن؟ لا يمكن التراجع عن هذا الإجراء."
      />

      {/* إشعارات */}
      <SnackbarNotification
        open={notification.open}
        onClose={() => setNotification({ ...notification, open: false })}
        message={notification.message}
        severity={notification.severity}
      />
    </Box>
  );
}

export default Warehouses;
