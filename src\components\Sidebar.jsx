import React, { useState } from 'react';
import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  Box,
  Divider,
  Avatar,
  Collapse,
  IconButton
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Dashboard as DashboardIcon,
  Inventory as InventoryIcon,
  People as PeopleIcon,
  Business as BusinessIcon,
  Receipt as ReceiptIcon,
  TrendingUp as TrendingUpIcon,
  Settings as SettingsIcon,
  ExitToApp as ExitToAppIcon,
  ExpandLess,
  ExpandMore,
  Store as StoreIcon,
  Add as AddIcon,
  List as ListIcon
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';

const StyledDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 260,
    backgroundColor: theme.palette.background.paper,
    borderRight: `2px solid ${theme.palette.divider}`,
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
    position: 'fixed',
    height: '100vh',
    zIndex: 1200,
    top: 0,
    right: 0
  }
}));

const LogoContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3, 2),
  backgroundColor: theme.palette.background.dark,
  borderBottom: `1px solid ${theme.palette.divider}`
}));

const StyledListItem = styled(ListItem)(({ theme, active }) => ({
  margin: theme.spacing(0.5, 1),
  borderRadius: theme.shape.borderRadius,
  transition: 'all 0.2s ease',
  cursor: 'pointer',

  ...(active && {
    backgroundColor: theme.palette.primary.main,
    color: theme.palette.primary.contrastText,
    '& .MuiListItemIcon-root': {
      color: theme.palette.primary.contrastText
    },
    '& .MuiListItemText-primary': {
      color: theme.palette.primary.contrastText,
      fontWeight: 500
    }
  }),

  '&:hover': {
    backgroundColor: active ?
      theme.palette.primary.dark :
      theme.palette.background.light,

    '& .MuiListItemIcon-root': {
      color: active ? theme.palette.primary.contrastText : theme.palette.primary.main
    }
  }
}));

const StyledListItemIcon = styled(ListItemIcon)(({ theme, active }) => ({
  minWidth: 40,
  color: active ? theme.palette.primary.contrastText : theme.palette.text.secondary,
  transition: 'color 0.2s ease',
  '& .MuiSvgIcon-root': {
    fontSize: '1.25rem'
  }
}));

const StyledListItemText = styled(ListItemText)(({ theme, active }) => ({
  '& .MuiTypography-root': {
    fontWeight: active ? 500 : 400,
    fontSize: '0.875rem',
    color: active ? theme.palette.primary.contrastText : theme.palette.text.primary
  }
}));

const UserProfile = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  backgroundColor: theme.palette.background.light,
  margin: theme.spacing(1),
  borderRadius: theme.shape.borderRadius,
  border: `1px solid ${theme.palette.divider}`
}));

const Sidebar = ({ open }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState({});

  const menuItems = [
    {
      path: '/dashboard',
      label: 'لوحة التحكم',
      icon: <DashboardIcon />
    },
    {
      path: '/products',
      label: 'المنتجات',
      icon: <InventoryIcon />
    },
    {
      path: '/customers',
      label: 'العملاء',
      icon: <PeopleIcon />
    },
    {
      path: '/suppliers',
      label: 'الموردين',
      icon: <BusinessIcon />
    },
    {
      label: 'الفواتير',
      icon: <ReceiptIcon />,
      children: [
        { path: '/invoices', label: 'عرض الفواتير', icon: <ListIcon /> },
        { path: '/create-invoice', label: 'فاتورة جديدة', icon: <AddIcon /> },
        { path: '/invoice-template', label: 'نموذج الفاتورة', icon: <ReceiptIcon /> }
      ]
    },
    {
      path: '/sales',
      label: 'المبيعات',
      icon: <TrendingUpIcon />
    }
  ];

  const handleItemClick = (item) => {
    if (item.children) {
      setExpandedItems(prev => ({
        ...prev,
        [item.label]: !prev[item.label]
      }));
    } else {
      navigate(item.path);
    }
  };

  const isActive = (path) => location.pathname === path;
  const isParentActive = (item) => {
    if (item.children) {
      return item.children.some(child => isActive(child.path));
    }
    return isActive(item.path);
  };

  const handleLogout = () => {
    localStorage.removeItem('isAuthenticated');
    navigate('/login');
  };

  return (
    <StyledDrawer
      variant="permanent"
      anchor="right"
      open={open}
      sx={{
        display: open ? 'block' : 'none',
        '& .MuiDrawer-paper': {
          transform: open ? 'translateX(0)' : 'translateX(100%)',
          transition: 'transform 0.3s ease'
        }
      }}
    >
      <LogoContainer>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar
            sx={{
              width: 40,
              height: 40,
              backgroundColor: 'primary.main',
              color: 'primary.contrastText'
            }}
          >
            <StoreIcon />
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
              النور
            </Typography>
            <Typography variant="caption" color="text.secondary">
              نظام إدارة الأعمال
            </Typography>
          </Box>
          {/* مؤشر حالة الاتصال */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Box
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: 'success.main',
                animation: 'pulse 2s infinite',
                '@keyframes pulse': {
                  '0%': { opacity: 1 },
                  '50%': { opacity: 0.5 },
                  '100%': { opacity: 1 }
                }
              }}
            />
            <Typography variant="caption" color="success.main" sx={{ fontSize: '0.7rem' }}>
              متصل
            </Typography>
          </Box>
        </Box>
      </LogoContainer>

      <UserProfile>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar
            sx={{
              width: 36,
              height: 36,
              backgroundColor: 'primary.main',
              color: 'primary.contrastText',
              fontSize: '1rem'
            }}
          >
            A
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="body2" sx={{ fontWeight: 500 }}>
              المدير العام
            </Typography>
            <Typography variant="caption" color="text.secondary">
              <EMAIL>
            </Typography>
          </Box>
        </Box>
      </UserProfile>

      <List sx={{ flex: 1, px: 1, py: 2 }}>
        {menuItems.map((item, index) => (
          <React.Fragment key={item.label}>
            <StyledListItem
              active={isParentActive(item)}
              onClick={() => handleItemClick(item)}
            >
              <StyledListItemIcon active={isParentActive(item)}>
                {item.icon}
              </StyledListItemIcon>
              <StyledListItemText
                primary={item.label}
                active={isParentActive(item)}
              />
              {item.children && (
                <IconButton size="small" sx={{ color: 'text.secondary' }}>
                  {expandedItems[item.label] ? <ExpandLess /> : <ExpandMore />}
                </IconButton>
              )}
            </StyledListItem>

            {item.children && (
              <Collapse in={expandedItems[item.label]} timeout="auto" unmountOnExit>
                <List component="div" disablePadding>
                  {item.children.map((child) => (
                    <StyledListItem
                      key={child.path}
                      active={isActive(child.path)}
                      onClick={() => navigate(child.path)}
                      sx={{ pl: 4 }}
                    >
                      <StyledListItemIcon active={isActive(child.path)}>
                        {child.icon}
                      </StyledListItemIcon>
                      <StyledListItemText
                        primary={child.label}
                        active={isActive(child.path)}
                      />
                    </StyledListItem>
                  ))}
                </List>
              </Collapse>
            )}
          </React.Fragment>
        ))}
      </List>

      <Divider sx={{ borderColor: 'divider' }} />

      <List sx={{ px: 1, py: 1 }}>
        <StyledListItem
          active={isActive('/settings')}
          onClick={() => navigate('/settings')}
        >
          <StyledListItemIcon active={isActive('/settings')}>
            <SettingsIcon />
          </StyledListItemIcon>
          <StyledListItemText
            primary="الإعدادات"
            active={isActive('/settings')}
          />
        </StyledListItem>

        <StyledListItem onClick={handleLogout}>
          <StyledListItemIcon>
            <ExitToAppIcon />
          </StyledListItemIcon>
          <StyledListItemText primary="تسجيل الخروج" />
        </StyledListItem>
      </List>

      {/* مؤشر الصفحة النشطة */}
      <Box sx={{
        p: 2,
        mt: 'auto',
        borderTop: '1px solid',
        borderColor: 'divider',
        backgroundColor: 'background.light'
      }}>
        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
          الصفحة الحالية:
        </Typography>
        <Typography variant="body2" sx={{ fontWeight: 500, color: 'primary.main' }}>
          {menuItems.find(item => {
            if (item.children) {
              return item.children.some(child => isActive(child.path));
            }
            return isActive(item.path);
          })?.label || 'غير محدد'}
        </Typography>
      </Box>
    </StyledDrawer>
  );
};

export default Sidebar;
