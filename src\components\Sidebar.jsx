import React, { useState } from 'react';
import { 
  Drawer, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  Typography, 
  Box,
  Divider,
  Avatar,
  Collapse,
  IconButton,
  Tooltip,
  Badge,
  Chip
} from '@mui/material';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Dashboard as DashboardIcon,
  Inventory as InventoryIcon,
  People as PeopleIcon,
  Business as BusinessIcon,
  Receipt as ReceiptIcon,
  TrendingUp as TrendingUpIcon,
  Settings as SettingsIcon,
  ExitToApp as ExitToAppIcon,
  ExpandLess,
  ExpandMore,
  Notifications as NotificationsIcon,
  AccountCircle as AccountIcon,
  Store as StoreIcon,
  Add as AddIcon,
  List as ListIcon
} from '@mui/icons-material';
import { styled, keyframes } from '@mui/material/styles';

const glow = keyframes`
  0% { box-shadow: 0 0 5px rgba(76, 175, 80, 0.3); }
  50% { box-shadow: 0 0 20px rgba(76, 175, 80, 0.6); }
  100% { box-shadow: 0 0 5px rgba(76, 175, 80, 0.3); }
`;

const slideIn = keyframes`
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
`;

const StyledDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 280,
    background: 'linear-gradient(180deg, #1a1a1a 0%, #2d2d2d 100%)',
    borderRight: '3px solid rgba(76, 175, 80, 0.3)',
    boxShadow: '4px 0 20px rgba(0, 0, 0, 0.3)',
    overflow: 'hidden',
    position: 'relative',
    '&::before': {
      content: '""',
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      height: '100%',
      background: 'linear-gradient(45deg, transparent 30%, rgba(76, 175, 80, 0.05) 50%, transparent 70%)',
      pointerEvents: 'none'
    }
  }
}));

const LogoContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3, 2),
  background: 'linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)',
  position: 'relative',
  overflow: 'hidden',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent)',
    animation: `${slideIn} 2s ease-in-out infinite`
  }
}));

const StyledListItem = styled(ListItem)(({ theme, active }) => ({
  margin: theme.spacing(0.5, 1),
  borderRadius: theme.spacing(1.5),
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  cursor: 'pointer',
  
  ...(active && {
    background: 'linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)',
    color: 'white',
    animation: `${glow} 2s ease-in-out infinite`,
    '&::before': {
      content: '""',
      position: 'absolute',
      left: 0,
      top: 0,
      bottom: 0,
      width: '4px',
      background: 'white',
      borderRadius: '0 4px 4px 0'
    }
  }),
  
  '&:hover': {
    background: active ? 
      'linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)' : 
      'rgba(76, 175, 80, 0.1)',
    transform: 'translateX(8px) scale(1.02)',
    boxShadow: '0 4px 20px rgba(76, 175, 80, 0.3)',
    
    '& .MuiListItemIcon-root': {
      transform: 'scale(1.2) rotate(5deg)'
    }
  },

  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent)',
    transition: 'left 0.5s ease'
  },

  '&:hover::after': {
    left: '100%'
  }
}));

const StyledListItemIcon = styled(ListItemIcon)(({ theme, active }) => ({
  minWidth: 48,
  color: active ? 'white' : 'rgba(255, 255, 255, 0.7)',
  transition: 'all 0.3s ease',
  '& .MuiSvgIcon-root': {
    fontSize: '1.5rem'
  }
}));

const StyledListItemText = styled(ListItemText)(({ theme, active }) => ({
  '& .MuiTypography-root': {
    fontWeight: active ? 700 : 500,
    fontSize: '0.95rem',
    color: active ? 'white' : 'rgba(255, 255, 255, 0.9)'
  }
}));

const UserProfile = styled(Box)(({ theme }) => ({
  padding: theme.spacing(2),
  background: 'rgba(76, 175, 80, 0.1)',
  margin: theme.spacing(1),
  borderRadius: theme.spacing(2),
  border: '1px solid rgba(76, 175, 80, 0.3)',
  backdropFilter: 'blur(10px)'
}));

const Sidebar = ({ open, onClose }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState({});

  const menuItems = [
    { 
      path: '/dashboard', 
      label: 'لوحة التحكم', 
      icon: <DashboardIcon />,
      badge: null
    },
    { 
      path: '/products', 
      label: 'المنتجات', 
      icon: <InventoryIcon />,
      badge: '150'
    },
    { 
      path: '/customers', 
      label: 'العملاء', 
      icon: <PeopleIcon />,
      badge: '45'
    },
    { 
      path: '/suppliers', 
      label: 'الموردين', 
      icon: <BusinessIcon />,
      badge: '12'
    },
    {
      label: 'الفواتير',
      icon: <ReceiptIcon />,
      badge: '8',
      children: [
        { path: '/invoices', label: 'عرض الفواتير', icon: <ListIcon /> },
        { path: '/create-invoice', label: 'فاتورة جديدة', icon: <AddIcon /> }
      ]
    },
    { 
      path: '/sales', 
      label: 'المبيعات', 
      icon: <TrendingUpIcon />,
      badge: null
    }
  ];

  const handleItemClick = (item) => {
    if (item.children) {
      setExpandedItems(prev => ({
        ...prev,
        [item.label]: !prev[item.label]
      }));
    } else {
      navigate(item.path);
      if (onClose) onClose();
    }
  };

  const isActive = (path) => location.pathname === path;
  const isParentActive = (item) => {
    if (item.children) {
      return item.children.some(child => isActive(child.path));
    }
    return isActive(item.path);
  };

  const handleLogout = () => {
    localStorage.removeItem('isAuthenticated');
    navigate('/login');
  };

  return (
    <StyledDrawer
      variant="persistent"
      anchor="right"
      open={open}
    >
      <LogoContainer>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar
            sx={{
              width: 48,
              height: 48,
              background: 'linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%)',
              color: '#4CAF50',
              fontSize: '1.5rem',
              fontWeight: 'bold',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.2)'
            }}
          >
            <StoreIcon sx={{ fontSize: 28 }} />
          </Avatar>
          <Box>
            <Typography variant="h6" sx={{ color: 'white', fontWeight: 700, mb: 0.5 }}>
              النور
            </Typography>
            <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.9)' }}>
              نظام إدارة الأعمال
            </Typography>
          </Box>
        </Box>
      </LogoContainer>

      <UserProfile>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar
            sx={{
              width: 40,
              height: 40,
              background: 'linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)'
            }}
          >
            <AccountIcon />
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="body2" sx={{ color: 'white', fontWeight: 600 }}>
              المدير العام
            </Typography>
            <Typography variant="caption" sx={{ color: 'rgba(255,255,255,0.7)' }}>
              <EMAIL>
            </Typography>
          </Box>
          <Tooltip title="الإشعارات">
            <IconButton size="small" sx={{ color: 'rgba(255,255,255,0.7)' }}>
              <Badge badgeContent={3} color="error">
                <NotificationsIcon />
              </Badge>
            </IconButton>
          </Tooltip>
        </Box>
      </UserProfile>

      <List sx={{ flex: 1, px: 1, py: 2 }}>
        {menuItems.map((item, index) => (
          <React.Fragment key={item.label}>
            <StyledListItem
              active={isParentActive(item)}
              onClick={() => handleItemClick(item)}
            >
              <StyledListItemIcon active={isParentActive(item)}>
                {item.icon}
              </StyledListItemIcon>
              <StyledListItemText 
                primary={item.label} 
                active={isParentActive(item)}
              />
              {item.badge && (
                <Chip
                  label={item.badge}
                  size="small"
                  sx={{
                    height: 20,
                    fontSize: '0.7rem',
                    background: 'rgba(76, 175, 80, 0.2)',
                    color: 'white',
                    border: '1px solid rgba(76, 175, 80, 0.5)'
                  }}
                />
              )}
              {item.children && (
                expandedItems[item.label] ? <ExpandLess sx={{ color: 'rgba(255,255,255,0.7)' }} /> : <ExpandMore sx={{ color: 'rgba(255,255,255,0.7)' }} />
              )}
            </StyledListItem>
            
            {item.children && (
              <Collapse in={expandedItems[item.label]} timeout="auto" unmountOnExit>
                <List component="div" disablePadding>
                  {item.children.map((child) => (
                    <StyledListItem
                      key={child.path}
                      active={isActive(child.path)}
                      onClick={() => {
                        navigate(child.path);
                        if (onClose) onClose();
                      }}
                      sx={{ pl: 4 }}
                    >
                      <StyledListItemIcon active={isActive(child.path)}>
                        {child.icon}
                      </StyledListItemIcon>
                      <StyledListItemText 
                        primary={child.label} 
                        active={isActive(child.path)}
                      />
                    </StyledListItem>
                  ))}
                </List>
              </Collapse>
            )}
          </React.Fragment>
        ))}
      </List>

      <Divider sx={{ borderColor: 'rgba(76, 175, 80, 0.3)' }} />
      
      <List sx={{ px: 1, py: 1 }}>
        <StyledListItem onClick={() => navigate('/settings')}>
          <StyledListItemIcon>
            <SettingsIcon />
          </StyledListItemIcon>
          <StyledListItemText primary="الإعدادات" />
        </StyledListItem>
        
        <StyledListItem onClick={handleLogout}>
          <StyledListItemIcon>
            <ExitToAppIcon />
          </StyledListItemIcon>
          <StyledListItemText primary="تسجيل الخروج" />
        </StyledListItem>
      </List>
    </StyledDrawer>
  );
};

export default Sidebar;
