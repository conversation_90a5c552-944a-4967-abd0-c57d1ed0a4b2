import React from 'react';
import { 
  Typography, 
  Grid, 
  Card, 
  CardContent,
  Container,
  Box
} from '@mui/material';
import SpeedIcon from '@mui/icons-material/Speed';
import DevicesIcon from '@mui/icons-material/Devices';
import SecurityIcon from '@mui/icons-material/Security';
import MainButton from './MainButton';

const features = [
  {
    title: "سريع وفعال",
    description: "تم تطوير الموقع باستخدام أحدث التقنيات لضمان سرعة الأداء",
    icon: <SpeedIcon sx={{ fontSize: 40, color: 'primary.main' }} />
  },
  {
    title: "تصميم متجاوب",
    description: "يعمل الموقع بشكل مثالي على جميع الأجهزة والشاشات",
    icon: <DevicesIcon sx={{ fontSize: 40, color: 'primary.main' }} />
  },
  {
    title: "آمن وموثوق",
    description: "نضمن أمان بياناتك وخصوصيتك بأحدث تقنيات الحماية",
    icon: <SecurityIcon sx={{ fontSize: 40, color: 'primary.main' }} />
  }
];

const Home = () => (
  <Box id="home">
    {/* Hero Section */}
    <Box
      sx={{
        background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
        color: 'white',
        pt: 15,
        pb: 10,
        textAlign: 'center',
        position: 'relative',
        overflow: 'hidden',
        '&::before': {
          content: '""',
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'url("/pattern.svg")',
          opacity: 0.1,
          zIndex: 1
        }
      }}
    >
      <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
        <Typography 
          variant="h1" 
          component="h1" 
          sx={{ 
            fontWeight: 900,
            fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4.5rem' },
            mb: 2,
            textShadow: '2px 2px 4px rgba(0,0,0,0.2)'
          }}
        >
          موقع النور
        </Typography>
        <Typography 
          variant="h4" 
          sx={{ 
            mb: 4,
            fontWeight: 300,
            fontSize: { xs: '1.2rem', sm: '1.5rem', md: '2rem' }
          }}
        >
          منصة احترافية مبنية بأحدث التقنيات
        </Typography>
        <MainButton 
          size="large"
          sx={{
            borderRadius: 8,
            px: 6,
            py: 2,
            fontSize: '1.2rem',
            backgroundColor: 'white',
            color: 'primary.main',
            '&:hover': {
              backgroundColor: 'rgba(255,255,255,0.9)'
            }
          }}
          onClick={() => document.getElementById('features').scrollIntoView({ behavior: 'smooth' })}
        >
          اكتشف المزيد
        </MainButton>
      </Container>
    </Box>

    {/* Features Section */}
    <Box 
      id="features" 
      sx={{ 
        py: 10,
        backgroundColor: '#f8f9fa'
      }}
    >
      <Container maxWidth="lg">
        <Typography 
          variant="h2" 
          component="h2" 
          align="center" 
          sx={{ 
            mb: 8,
            fontWeight: 'bold',
            color: 'primary.main'
          }}
        >
          مميزاتنا
        </Typography>
        <Grid container spacing={4}>
          {features.map((feature, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Card 
                sx={{ 
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  borderRadius: 4,
                  transition: 'transform 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: 6
                  }
                }}
              >
                <CardContent sx={{ flexGrow: 1, textAlign: 'center' }}>
                  <Box sx={{ mb: 2 }}>
                    {feature.icon}
                  </Box>
                  <Typography gutterBottom variant="h5" component="h3" sx={{ fontWeight: 'bold' }}>
                    {feature.title}
                  </Typography>
                  <Typography color="text.secondary">
                    {feature.description}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>
    </Box>
  </Box>
);

export default Home;
