import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  InputAdornment,
  IconButton,
  Chip,
  Button,
  Menu,
  MenuItem,
  Grid,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import AddIcon from '@mui/icons-material/Add';
import { useApp } from '../context/AppContext';
import FormDialog from '../components/FormDialog';
import ConfirmDialog from '../components/ConfirmDialog';
import SnackbarNotification from '../components/SnackbarNotification';

// نموذج إضافة/تعديل منتج
const ProductForm = ({ open, onClose, product = null, onSave }) => {
  const [productData, setProductData] = useState({
    code: '',
    name: '',
    category: '',
    brand: '',
    unit: '',
    purchasePrice: '',
    salePrice: '',
    wholesalePrice: '',
    distributorPrice: '',
    quantity: '',
    minQuantity: '',
    description: ''
  });

  // تحديث البيانات عند تمرير منتج للتعديل
  React.useEffect(() => {
    if (product) {
      setProductData(product);
    } else {
      setProductData({
        code: '',
        name: '',
        category: '',
        brand: '',
        unit: '',
        purchasePrice: '',
        salePrice: '',
        wholesalePrice: '',
        distributorPrice: '',
        quantity: '',
        minQuantity: '',
        description: ''
      });
    }
  }, [product]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setProductData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(productData);
    onClose();
  };

  return (
  <FormDialog
      open={open}
      onClose={onClose}
      onSubmit={handleSubmit}
      title={product ? 'تعديل المنتج' : 'إضافة منتج جديد'}
      maxWidth="md"
    >
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6}>
              <TextField
                name="code"
                label="رمز المنتج"
                value={productData.code}
                onChange={handleChange}
                fullWidth
                required
                variant="outlined"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="name"
                label="اسم المنتج"
                value={productData.name}
                onChange={handleChange}
                fullWidth
                required
                variant="outlined"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>الفئة</InputLabel>
                <Select
                  name="category"
                  value={productData.category}
                  onChange={handleChange}
                  label="الفئة"
                >
                  <MenuItem value="أجهزة كمبيوتر">أجهزة كمبيوتر</MenuItem>
                  <MenuItem value="طابعات">طابعات</MenuItem>
                  <MenuItem value="مستلزمات">مستلزمات</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="brand"
                label="العلامة التجارية"
                value={productData.brand}
                onChange={handleChange}
                fullWidth
                variant="outlined"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>وحدة القياس</InputLabel>
                <Select
                  name="unit"
                  value={productData.unit}
                  onChange={handleChange}
                  label="وحدة القياس"
                  required
                >
                  <MenuItem value="قطعة">قطعة</MenuItem>
                  <MenuItem value="صندوق">صندوق</MenuItem>
                  <MenuItem value="كرتون">كرتون</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="quantity"
                label="الكمية الحالية"
                type="number"
                value={productData.quantity}
                onChange={handleChange}
                fullWidth
                required
                variant="outlined"
                margin="normal"
                InputProps={{
                  inputProps: { min: 0 }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="minQuantity"
                label="الحد الأدنى للكمية"
                type="number"
                value={productData.minQuantity}
                onChange={handleChange}
                fullWidth
                required
                variant="outlined"
                margin="normal"
                InputProps={{
                  inputProps: { min: 0 }
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="purchasePrice"
                label="سعر الشراء"
                type="number"
                value={productData.purchasePrice}
                onChange={handleChange}
                fullWidth
                required
                variant="outlined"
                margin="normal"
                InputProps={{
                  inputProps: { min: 0 },
                  startAdornment: <InputAdornment position="start">ر.س</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="salePrice"
                label="سعر البيع"
                type="number"
                value={productData.salePrice}
                onChange={handleChange}
                fullWidth
                required
                variant="outlined"
                margin="normal"
                InputProps={{
                  inputProps: { min: 0 },
                  startAdornment: <InputAdornment position="start">ر.س</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="wholesalePrice"
                label="سعر الجملة"
                type="number"
                value={productData.wholesalePrice}
                onChange={handleChange}
                fullWidth
                variant="outlined"
                margin="normal"
                InputProps={{
                  inputProps: { min: 0 },
                  startAdornment: <InputAdornment position="start">ر.س</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                name="distributorPrice"
                label="سعر الموزع"
                type="number"
                value={productData.distributorPrice}
                onChange={handleChange}
                fullWidth
                variant="outlined"
                margin="normal"
                InputProps={{
                  inputProps: { min: 0 },
                  startAdornment: <InputAdornment position="start">ر.س</InputAdornment>,
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="description"
                label="وصف المنتج"
                value={productData.description}
                onChange={handleChange}
                fullWidth
                multiline
                rows={3}
                variant="outlined"
                margin="normal"
              />
            </Grid>
          </Grid>
    </FormDialog>
  );
};

const Products = () => {
  const { state, addProduct, updateProduct, deleteProduct } = useApp();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterAnchorEl, setFilterAnchorEl] = useState(null);
  const [selectedFilters, setSelectedFilters] = useState([]);
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState({ open: false, productId: null });
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });

  const handleFilterClick = (event) => {
    setFilterAnchorEl(event.currentTarget);
  };

  const handleFilterClose = () => {
    setFilterAnchorEl(null);
  };

  const handleFilterSelect = (filter) => {
    if (!selectedFilters.includes(filter)) {
      setSelectedFilters([...selectedFilters, filter]);
    }
    handleFilterClose();
  };

  const handleFilterDelete = (filterToDelete) => {
    setSelectedFilters(selectedFilters.filter(filter => filter !== filterToDelete));
  };

  // وظائف إدارة المنتجات
  const handleSaveProduct = (productData) => {
    if (editingProduct) {
      updateProduct({ ...productData, id: editingProduct.id });
      setNotification({ open: true, message: 'تم تحديث المنتج بنجاح', severity: 'success' });
    } else {
      addProduct(productData);
      setNotification({ open: true, message: 'تم إضافة المنتج بنجاح', severity: 'success' });
    }
    setEditingProduct(null);
  };

  const handleEditProduct = (product) => {
    setEditingProduct(product);
    setOpenAddDialog(true);
  };

  const handleDeleteProduct = (productId) => {
    setDeleteConfirm({ open: true, productId });
  };

  const confirmDelete = () => {
    deleteProduct(deleteConfirm.productId);
    setDeleteConfirm({ open: false, productId: null });
    setNotification({ open: true, message: 'تم حذف المنتج بنجاح', severity: 'success' });
  };

  const handleCloseDialog = () => {
    setOpenAddDialog(false);
    setEditingProduct(null);
  };

  return (
    <Box>
      {/* رأس الصفحة */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600 }}>المنتجات</Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpenAddDialog(true)}
          sx={{
            bgcolor: '#005B48',
            '&:hover': { bgcolor: '#004a3b' }
          }}
        >
          إضافة منتج
        </Button>
      </Box>

      {/* شريط البحث والفلترة */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          mb: 3,
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          borderRadius: 3
        }}
      >
        <Box sx={{ display: 'flex', gap: 2 }}>
          <TextField
            placeholder="بحث..."
            variant="outlined"
            size="small"
            fullWidth
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon sx={{ color: '#9e9e9e' }} />
                </InputAdornment>
              ),
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                bgcolor: '#f5f7fa',
                '&:hover fieldset': {
                  borderColor: '#bdbdbd',
                },
              }
            }}
          />
          <Button
            variant="outlined"
            startIcon={<FilterListIcon />}
            onClick={handleFilterClick}
            sx={{
              minWidth: 130,
              borderColor: '#e0e0e0',
              color: '#424242',
              '&:hover': {
                borderColor: '#bdbdbd',
                bgcolor: '#f5f7fa',
              }
            }}
          >
            تصفية
          </Button>
        </Box>

        {/* الفلاتر المحددة */}
        {selectedFilters.length > 0 && (
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {selectedFilters.map((filter) => (
              <Chip
                key={filter}
                label={filter}
                onDelete={() => handleFilterDelete(filter)}
                sx={{
                  bgcolor: '#e3f2fd',
                  color: '#1976d2',
                  '& .MuiChip-deleteIcon': {
                    color: '#1976d2',
                    '&:hover': { color: '#1565c0' }
                  }
                }}
              />
            ))}
          </Box>
        )}
      </Paper>

      {/* جدول المنتجات */}
      <TableContainer
        component={Paper}
        elevation={0}
        sx={{ borderRadius: 3 }}
      >
        <Table>
          <TableHead>
            <TableRow>
              <TableCell sx={{ fontWeight: 'bold' }}>#</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الكود</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>اسم المنتج</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>سعر البيع</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>سعر الجملة</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>سعر الموزع</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>سعر الشراء</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>الكمية</TableCell>
              <TableCell sx={{ fontWeight: 'bold' }}>إجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {state.products
              .filter(product =>
                product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                product.code.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .map((product) => (
              <TableRow
                key={product.id}
                sx={{ '&:hover': { bgcolor: '#f5f7fa' } }}
              >
                <TableCell>{product.id}</TableCell>
                <TableCell>{product.code}</TableCell>
                <TableCell>{product.name}</TableCell>
                <TableCell>{product.salePrice?.toLocaleString()} ر.س</TableCell>
                <TableCell>{product.wholesalePrice?.toLocaleString()} ر.س</TableCell>
                <TableCell>{product.distributorPrice?.toLocaleString()} ر.س</TableCell>
                <TableCell>{product.purchasePrice?.toLocaleString()} ر.س</TableCell>
                <TableCell>
                  <Chip
                    label={`${product.quantity} ${product.unit}`}
                    color={product.quantity <= product.minQuantity ? 'error' : 'success'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleEditProduct(product)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteProduct(product.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* قائمة الفلترة */}
      <Menu
        anchorEl={filterAnchorEl}
        open={Boolean(filterAnchorEl)}
        onClose={handleFilterClose}
      >
        <MenuItem onClick={() => handleFilterSelect('السعر')}>السعر</MenuItem>
        <MenuItem onClick={() => handleFilterSelect('الكمية')}>الكمية</MenuItem>
        <MenuItem onClick={() => handleFilterSelect('النوع')}>النوع</MenuItem>
      </Menu>

      {/* نافذة إضافة/تعديل منتج */}
      <ProductForm
        open={openAddDialog}
        onClose={handleCloseDialog}
        product={editingProduct}
        onSave={handleSaveProduct}
      />

      {/* حوار تأكيد الحذف */}
      <ConfirmDialog
        open={deleteConfirm.open}
        onClose={() => setDeleteConfirm({ open: false, productId: null })}
        onConfirm={confirmDelete}
        title="حذف المنتج"
        message="هل أنت متأكد من أنك تريد حذف هذا المنتج؟ لا يمكن التراجع عن هذا الإجراء."
      />

      {/* إشعارات */}
      <SnackbarNotification
        open={notification.open}
        onClose={() => setNotification({ ...notification, open: false })}
        message={notification.message}
        severity={notification.severity}
      />
    </Box>
  );
};

export default Products;
