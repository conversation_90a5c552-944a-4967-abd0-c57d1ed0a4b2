import React from 'react';
import { But<PERSON>, Box } from '@mui/material';
import { styled, keyframes } from '@mui/material/styles';

const shimmer = keyframes`
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
`;

const ripple = keyframes`
  0% { transform: scale(0); opacity: 1; }
  100% { transform: scale(4); opacity: 0; }
`;

const StyledButton = styled(Button)(({ theme, variant, color, size, animated = true }) => {
  const getGradient = () => {
    switch (color) {
      case 'primary':
        return 'linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)';
      case 'secondary':
        return 'linear-gradient(135deg, #2196F3 0%, #64B5F6 100%)';
      case 'success':
        return 'linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%)';
      case 'warning':
        return 'linear-gradient(135deg, #FF9800 0%, #FFC107 100%)';
      case 'error':
        return 'linear-gradient(135deg, #F44336 0%, #E57373 100%)';
      case 'info':
        return 'linear-gradient(135deg, #2196F3 0%, #03A9F4 100%)';
      default:
        return 'linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)';
    }
  };

  const getShadow = () => {
    switch (color) {
      case 'primary':
        return '0 4px 20px rgba(76, 175, 80, 0.3)';
      case 'secondary':
        return '0 4px 20px rgba(33, 150, 243, 0.3)';
      case 'success':
        return '0 4px 20px rgba(76, 175, 80, 0.3)';
      case 'warning':
        return '0 4px 20px rgba(255, 152, 0, 0.3)';
      case 'error':
        return '0 4px 20px rgba(244, 67, 54, 0.3)';
      case 'info':
        return '0 4px 20px rgba(33, 150, 243, 0.3)';
      default:
        return '0 4px 20px rgba(76, 175, 80, 0.3)';
    }
  };

  const getPadding = () => {
    switch (size) {
      case 'small':
        return '8px 16px';
      case 'large':
        return '14px 32px';
      default:
        return '12px 24px';
    }
  };

  return {
    background: variant === 'contained' ? getGradient() : 'transparent',
    border: variant === 'outlined' ? `2px solid ${theme.palette[color]?.main || '#4CAF50'}` : 'none',
    borderRadius: theme.spacing(1.5),
    padding: getPadding(),
    fontSize: size === 'large' ? '1.1rem' : size === 'small' ? '0.8rem' : '1rem',
    fontWeight: 600,
    textTransform: 'none',
    position: 'relative',
    overflow: 'hidden',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    boxShadow: variant === 'contained' ? getShadow() : 'none',
    
    '&::before': animated ? {
      content: '""',
      position: 'absolute',
      top: 0,
      left: '-100%',
      width: '100%',
      height: '100%',
      background: 'linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent)',
      transition: 'left 0.5s ease'
    } : {},

    '&::after': {
      content: '""',
      position: 'absolute',
      top: '50%',
      left: '50%',
      width: '0',
      height: '0',
      borderRadius: '50%',
      background: 'rgba(255, 255, 255, 0.3)',
      transform: 'translate(-50%, -50%)',
      transition: 'width 0.3s ease, height 0.3s ease'
    },

    '&:hover': {
      transform: 'translateY(-2px) scale(1.02)',
      boxShadow: variant === 'contained' ? 
        getShadow().replace('0.3', '0.5').replace('20px', '30px') : 
        '0 8px 25px rgba(0, 0, 0, 0.15)',
      
      '&::before': animated ? {
        left: '100%'
      } : {},

      '&::after': {
        width: '300px',
        height: '300px',
        animation: `${ripple} 0.6s ease-out`
      }
    },

    '&:active': {
      transform: 'translateY(0) scale(0.98)'
    },

    '&:disabled': {
      background: theme.palette.grey[300],
      color: theme.palette.grey[500],
      boxShadow: 'none',
      transform: 'none',
      '&:hover': {
        transform: 'none',
        boxShadow: 'none'
      }
    }
  };
});

const AnimatedButton = ({ 
  children, 
  startIcon, 
  endIcon, 
  loading = false,
  animated = true,
  ...props 
}) => {
  return (
    <StyledButton 
      animated={animated}
      startIcon={loading ? null : startIcon}
      endIcon={loading ? null : endIcon}
      disabled={loading}
      {...props}
    >
      {loading ? (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Box
            sx={{
              width: 16,
              height: 16,
              border: '2px solid currentColor',
              borderTop: '2px solid transparent',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite',
              '@keyframes spin': {
                '0%': { transform: 'rotate(0deg)' },
                '100%': { transform: 'rotate(360deg)' }
              }
            }}
          />
          جاري التحميل...
        </Box>
      ) : (
        children
      )}
    </StyledButton>
  );
};

export default AnimatedButton;
