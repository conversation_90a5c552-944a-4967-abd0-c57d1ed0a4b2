import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  TextField,
  InputAdornment,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import StorefrontIcon from '@mui/icons-material/Storefront';
import PaidIcon from '@mui/icons-material/Paid';
import ShowChartIcon from '@mui/icons-material/ShowChart';
import { useApp } from '../context/AppContext';
import FormDialog from '../components/FormDialog';
import ConfirmDialog from '../components/ConfirmDialog';
import SnackbarNotification from '../components/SnackbarNotification';

// نموذج إضافة/تعديل عملية بيع
const SaleForm = ({ open, onClose, sale = null, onSave }) => {
  const { state } = useApp();
  const [saleData, setSaleData] = useState({
    date: new Date().toISOString().split('T')[0],
    customer: '',
    items: [],
    subtotal: 0,
    tax: 0,
    discount: 0,
    total: 0,
    paymentMethod: 'نقدي',
    status: 'مكتملة',
    notes: ''
  });

  const [selectedProduct, setSelectedProduct] = useState('');
  const [productQuantity, setProductQuantity] = useState(1);
  const [productPrice, setProductPrice] = useState(0);

  React.useEffect(() => {
    if (sale) {
      setSaleData(sale);
    } else {
      setSaleData(prev => ({
        ...prev,
        date: new Date().toISOString().split('T')[0]
      }));
    }
  }, [sale]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setSaleData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleAddItem = () => {
    if (!selectedProduct) return;

    const product = state.products.find(p => p.id === selectedProduct);
    if (!product) return;

    const newItem = {
      id: Date.now(),
      productId: product.id,
      productName: product.name,
      quantity: productQuantity,
      price: productPrice || product.salePrice,
      total: productQuantity * (productPrice || product.salePrice)
    };

    const updatedItems = [...saleData.items, newItem];
    const subtotal = updatedItems.reduce((sum, item) => sum + item.total, 0);
    const tax = subtotal * 0.15;
    const total = subtotal + tax - saleData.discount;

    setSaleData(prev => ({
      ...prev,
      items: updatedItems,
      subtotal,
      tax,
      total
    }));

    setSelectedProduct('');
    setProductQuantity(1);
    setProductPrice(0);
  };

  const handleRemoveItem = (itemId) => {
    const updatedItems = saleData.items.filter(item => item.id !== itemId);
    const subtotal = updatedItems.reduce((sum, item) => sum + item.total, 0);
    const tax = subtotal * 0.15;
    const total = subtotal + tax - saleData.discount;

    setSaleData(prev => ({
      ...prev,
      items: updatedItems,
      subtotal,
      tax,
      total
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (saleData.items.length === 0) {
      alert('يرجى إضافة منتج واحد على الأقل');
      return;
    }
    onSave(saleData);
    onClose();
  };

  return (
    <FormDialog
      open={open}
      onClose={onClose}
      onSubmit={handleSubmit}
      title={sale ? 'تعديل عملية البيع' : 'إضافة عملية بيع جديدة'}
      maxWidth="lg"
    >
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <TextField
            name="date"
            label="تاريخ البيع"
            type="date"
            value={saleData.date}
            onChange={handleChange}
            fullWidth
            required
            variant="outlined"
            margin="normal"
            InputLabelProps={{ shrink: true }}
          />
        </Grid>
        <Grid item xs={12} md={6}>
          <FormControl fullWidth margin="normal">
            <InputLabel>العميل</InputLabel>
            <Select
              name="customer"
              value={saleData.customer}
              onChange={handleChange}
              label="العميل"
              required
            >
              {state.customers.map(customer => (
                <MenuItem key={customer.id} value={customer.name}>
                  {customer.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        {/* إضافة المنتجات */}
        <Grid item xs={12}>
          <Typography variant="h6" sx={{ mb: 2, color: '#005B48' }}>
            إضافة المنتجات
          </Typography>
          <Paper sx={{ p: 2, mb: 2 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <FormControl fullWidth>
                  <InputLabel>المنتج</InputLabel>
                  <Select
                    value={selectedProduct}
                    onChange={(e) => {
                      setSelectedProduct(e.target.value);
                      const product = state.products.find(p => p.id === e.target.value);
                      if (product) {
                        setProductPrice(product.salePrice);
                      }
                    }}
                    label="المنتج"
                  >
                    {state.products.map(product => (
                      <MenuItem key={product.id} value={product.id}>
                        {product.name} - {product.salePrice} ر.س
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <TextField
                  label="الكمية"
                  type="number"
                  value={productQuantity}
                  onChange={(e) => setProductQuantity(parseInt(e.target.value) || 1)}
                  fullWidth
                  inputProps={{ min: 1 }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  label="السعر"
                  type="number"
                  value={productPrice}
                  onChange={(e) => setProductPrice(parseFloat(e.target.value) || 0)}
                  fullWidth
                  InputProps={{
                    endAdornment: <InputAdornment position="end">ر.س</InputAdornment>
                  }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <Button
                  variant="contained"
                  onClick={handleAddItem}
                  disabled={!selectedProduct}
                  fullWidth
                  sx={{ bgcolor: '#005B48', '&:hover': { bgcolor: '#004a3b' } }}
                >
                  إضافة
                </Button>
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* قائمة المنتجات */}
        {saleData.items.length > 0 && (
          <Grid item xs={12}>
            <TableContainer component={Paper}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>المنتج</TableCell>
                    <TableCell>الكمية</TableCell>
                    <TableCell>السعر</TableCell>
                    <TableCell>الإجمالي</TableCell>
                    <TableCell>إجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {saleData.items.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>{item.productName}</TableCell>
                      <TableCell>{item.quantity}</TableCell>
                      <TableCell>{item.price} ر.س</TableCell>
                      <TableCell>{item.total} ر.س</TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => handleRemoveItem(item.id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
        )}

        {/* ملخص البيع */}
        {saleData.items.length > 0 && (
          <Grid item xs={12} md={6} sx={{ ml: 'auto' }}>
            <Paper sx={{ p: 2 }}>
              <Typography variant="h6" sx={{ mb: 2, color: '#005B48' }}>
                ملخص البيع
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>المجموع الفرعي:</Typography>
                <Typography>{saleData.subtotal.toFixed(2)} ر.س</Typography>
              </Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography>الضريبة (15%):</Typography>
                <Typography>{saleData.tax.toFixed(2)} ر.س</Typography>
              </Box>
              <Divider sx={{ my: 1 }} />
              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                  الإجمالي:
                </Typography>
                <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#005B48' }}>
                  {saleData.total.toFixed(2)} ر.س
                </Typography>
              </Box>
            </Paper>
          </Grid>
        )}
      </Grid>
    </FormDialog>
  );
};

function Sales() {
  const { state, addSale, updateSale, deleteSale } = useApp();
  const [searchQuery, setSearchQuery] = useState('');
  const [openAddDialog, setOpenAddDialog] = useState(false);
  const [editingSale, setEditingSale] = useState(null);
  const [deleteConfirm, setDeleteConfirm] = useState({ open: false, saleId: null });
  const [notification, setNotification] = useState({ open: false, message: '', severity: 'success' });

  // وظائف إدارة المبيعات
  const handleSaveSale = (saleData) => {
    if (editingSale) {
      updateSale({ ...saleData, id: editingSale.id });
      setNotification({ open: true, message: 'تم تحديث عملية البيع بنجاح', severity: 'success' });
    } else {
      addSale(saleData);
      setNotification({ open: true, message: 'تم إضافة عملية البيع بنجاح', severity: 'success' });
    }
    setEditingSale(null);
  };

  const handleEditSale = (sale) => {
    setEditingSale(sale);
    setOpenAddDialog(true);
  };

  const handleDeleteSale = (saleId) => {
    setDeleteConfirm({ open: true, saleId });
  };

  const confirmDelete = () => {
    deleteSale(deleteConfirm.saleId);
    setDeleteConfirm({ open: false, saleId: null });
    setNotification({ open: true, message: 'تم حذف عملية البيع بنجاح', severity: 'success' });
  };

  const handleCloseDialog = () => {
    setOpenAddDialog(false);
    setEditingSale(null);
  };

  // حساب الإحصائيات
  const totalSales = state.sales.reduce((sum, sale) => sum + sale.total, 0);
  const completedSales = state.sales.filter(sale => sale.status === 'مكتملة').length;
  const averageSale = state.sales.length > 0 ? totalSales / state.sales.length : 0;
  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" sx={{ color: '#005B48', fontWeight: 'bold' }}>
          المبيعات
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setOpenAddDialog(true)}
          sx={{
            backgroundColor: '#005B48',
            '&:hover': { backgroundColor: '#00483A' }
          }}
        >
          إضافة عملية بيع
        </Button>
      </Box>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#005B48', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <StorefrontIcon fontSize="large" />
              <Box>
                <Typography variant="h6">إجمالي المبيعات</Typography>
                <Typography variant="h4">{totalSales.toLocaleString()}</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#00796B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <PaidIcon fontSize="large" />
              <Box>
                <Typography variant="h6">المدفوعات</Typography>
                <Typography variant="h4">{completedSales}</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#00897B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <ShowChartIcon fontSize="large" />
              <Box>
                <Typography variant="h6">معدل المبيعات اليومي</Typography>
                <Typography variant="h4">{averageSale.toLocaleString()}</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="بحث في المبيعات..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell>التاريخ</TableCell>
              <TableCell>العميل</TableCell>
              <TableCell>رقم الفاتورة</TableCell>
              <TableCell>الإجمالي</TableCell>
              <TableCell>حالة الطلب</TableCell>
              <TableCell>حالة الدفع</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {state.sales
              .filter(sale =>
                sale.customer.toLowerCase().includes(searchQuery.toLowerCase()) ||
                sale.date.includes(searchQuery)
              )
              .map((sale) => (
              <TableRow key={sale.id}>
                <TableCell>{sale.date}</TableCell>
                <TableCell>{sale.customer}</TableCell>
                <TableCell>SALE-{sale.id}</TableCell>
                <TableCell>{sale.total.toLocaleString()} ر.س</TableCell>
                <TableCell>
                  <Chip
                    label={sale.status}
                    color={sale.status === 'مكتملة' ? 'success' : 'warning'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={sale.paymentMethod}
                    color={
                      sale.paymentMethod === 'نقدي' ? 'success' :
                      sale.paymentMethod === 'بطاقة ائتمان' ? 'info' : 'warning'
                    }
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleEditSale(sale)}
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteSale(sale.id)}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* نافذة إضافة/تعديل عملية بيع */}
      <SaleForm
        open={openAddDialog}
        onClose={handleCloseDialog}
        sale={editingSale}
        onSave={handleSaveSale}
      />

      {/* حوار تأكيد الحذف */}
      <ConfirmDialog
        open={deleteConfirm.open}
        onClose={() => setDeleteConfirm({ open: false, saleId: null })}
        onConfirm={confirmDelete}
        title="حذف عملية البيع"
        message="هل أنت متأكد من أنك تريد حذف عملية البيع هذه؟ لا يمكن التراجع عن هذا الإجراء."
      />

      {/* إشعارات */}
      <SnackbarNotification
        open={notification.open}
        onClose={() => setNotification({ ...notification, open: false })}
        message={notification.message}
        severity={notification.severity}
      />
    </Box>
  );
}

export default Sales;
