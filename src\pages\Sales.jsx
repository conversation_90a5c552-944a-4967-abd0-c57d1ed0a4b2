import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Button,
  TextField,
  InputAdornment,
  Chip
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SearchIcon from '@mui/icons-material/Search';
import StorefrontIcon from '@mui/icons-material/Storefront';
import PaidIcon from '@mui/icons-material/Paid';
import ShowChartIcon from '@mui/icons-material/ShowChart';

// بيانات تجريبية للمبيعات
const salesData = [
  { 
    id: 1, 
    date: '2025-05-25',
    customer: 'أحمد محمد',
    invoice: 'SALE-001',
    total: 3500,
    status: 'مكتمل',
    paymentStatus: 'مدفوع'
  },
  { 
    id: 2, 
    date: '2025-05-24',
    customer: 'محمد علي',
    invoice: 'SALE-002',
    total: 2800,
    status: 'مكتمل',
    paymentStatus: 'جزئي'
  },
  { 
    id: 3, 
    date: '2025-05-23',
    customer: 'سارة أحمد',
    invoice: 'SALE-003',
    total: 4200,
    status: 'قيد التنفيذ',
    paymentStatus: 'غير مدفوع'
  },
];

function Sales() {
  return (
    <Box>
      <Box sx={{ mb: 4, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" component="h1" sx={{ color: '#005B48', fontWeight: 'bold' }}>
          المبيعات
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          sx={{
            backgroundColor: '#005B48',
            '&:hover': { backgroundColor: '#00483A' }
          }}
        >
          إضافة فاتورة بيع
        </Button>
      </Box>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#005B48', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <StorefrontIcon fontSize="large" />
              <Box>
                <Typography variant="h6">إجمالي المبيعات</Typography>
                <Typography variant="h4">10,500</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#00796B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <PaidIcon fontSize="large" />
              <Box>
                <Typography variant="h6">المدفوعات</Typography>
                <Typography variant="h4">6,300</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <Card sx={{ backgroundColor: '#00897B', color: 'white' }}>
            <CardContent sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <ShowChartIcon fontSize="large" />
              <Box>
                <Typography variant="h6">معدل المبيعات اليومي</Typography>
                <Typography variant="h4">3,500</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="بحث في المبيعات..."
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
              <TableCell>التاريخ</TableCell>
              <TableCell>العميل</TableCell>
              <TableCell>رقم الفاتورة</TableCell>
              <TableCell>الإجمالي</TableCell>
              <TableCell>حالة الطلب</TableCell>
              <TableCell>حالة الدفع</TableCell>
              <TableCell>الإجراءات</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {salesData.map((sale) => (
              <TableRow key={sale.id}>
                <TableCell>{sale.date}</TableCell>
                <TableCell>{sale.customer}</TableCell>
                <TableCell>{sale.invoice}</TableCell>
                <TableCell>{sale.total} ج.م</TableCell>
                <TableCell>
                  <Chip
                    label={sale.status}
                    color={sale.status === 'مكتمل' ? 'success' : 'warning'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={sale.paymentStatus}
                    color={
                      sale.paymentStatus === 'مدفوع' ? 'success' :
                      sale.paymentStatus === 'جزئي' ? 'warning' : 'error'
                    }
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <IconButton size="small" color="primary">
                    <EditIcon />
                  </IconButton>
                  <IconButton size="small" color="error">
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
}

export default Sales;
