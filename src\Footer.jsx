import React from 'react';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Typography from '@mui/material/Typography';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import IconButton from '@mui/material/IconButton';
import FacebookIcon from '@mui/icons-material/Facebook';
import TwitterIcon from '@mui/icons-material/Twitter';
import InstagramIcon from '@mui/icons-material/Instagram';
import LinkedInIcon from '@mui/icons-material/LinkedIn';

const socialLinks = [
  { icon: <FacebookIcon />, url: '#', label: 'Facebook' },
  { icon: <TwitterIcon />, url: '#', label: 'Twitter' },
  { icon: <InstagramIcon />, url: '#', label: 'Instagram' },
  { icon: <LinkedInIcon />, url: '#', label: 'LinkedIn' },
];

const Footer = () => (
  <Box
    component="footer"
    sx={{
      backgroundColor: 'primary.main',
      color: 'white',
      py: 6,
      position: 'relative',
      '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: 'url("/pattern.svg")',
        opacity: 0.1,
        zIndex: 1,
      }
    }}
  >
    <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
      <Grid container spacing={4}>
        <Grid item xs={12} md={4}>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
            النور
          </Typography>
          <Typography variant="body2" sx={{ opacity: 0.8 }}>
            نقدم حلولاً تقنية مبتكرة لمساعدة عملائنا على النجاح في العالم الرقمي.
          </Typography>
        </Grid>
        <Grid item xs={12} md={4}>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
            روابط مهمة
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            {['الرئيسية', 'عن الموقع', 'تواصل معنا'].map((text) => (
              <Link
                key={text}
                href="#"
                color="inherit"
                sx={{
                  mb: 1,
                  opacity: 0.8,
                  textDecoration: 'none',
                  '&:hover': {
                    opacity: 1,
                    textDecoration: 'underline',
                  },
                }}
              >
                {text}
              </Link>
            ))}
          </Box>
        </Grid>
        <Grid item xs={12} md={4}>
          <Typography variant="h6" gutterBottom sx={{ fontWeight: 'bold' }}>
            تابعنا
          </Typography>
          <Box sx={{ display: 'flex', gap: 1 }}>
            {socialLinks.map((social) => (
              <IconButton
                key={social.label}
                href={social.url}
                aria-label={social.label}
                sx={{
                  color: 'white',
                  opacity: 0.8,
                  '&:hover': {
                    opacity: 1,
                    backgroundColor: 'rgba(255,255,255,0.1)',
                  },
                }}
              >
                {social.icon}
              </IconButton>
            ))}
          </Box>
        </Grid>
      </Grid>
      <Typography
        variant="body2"
        align="center"
        sx={{ mt: 8, opacity: 0.8, borderTop: '1px solid rgba(255,255,255,0.1)', pt: 3 }}
      >
        © {new Date().getFullYear()} النور. جميع الحقوق محفوظة.
      </Typography>
    </Container>
  </Box>
);

export default Footer;
