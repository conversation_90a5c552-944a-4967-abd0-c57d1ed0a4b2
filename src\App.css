#root {
  max-width: none;
  margin: 0;
  padding: 0;
  text-align: initial;
}

.MuiDrawer-paper {
  border-left: 1px solid #e0e0e0;
  box-shadow: 0 0 8px #eee;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

.sidebar-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: white;
  text-decoration: none;
  transition: background-color 0.3s;
  margin-bottom: 4px;
  border-radius: 8px;
  margin-left: 12px;
  margin-right: 12px;
}

.sidebar-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-menu-item.active {
  background-color: rgba(255, 255, 255, 0.15);
}

.sidebar-menu-item .MuiSvgIcon-root {
  margin-left: 12px;
  font-size: 20px;
}

.search-input {
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.search-input:hover {
  background-color: #fff;
  border-color: #bdbdbd;
}

.search-input .MuiOutlinedInput-root {
  transition: all 0.3s;
}

.search-input .MuiOutlinedInput-root:hover {
  background-color: #fafafa;
}

.content-card {
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  transition: all 0.3s;
}

.content-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.08);
}

/* Custom scrollbar for sidebar */
.sidebar-scroll::-webkit-scrollbar {
  width: 6px;
}

.sidebar-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar-scroll::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.sidebar-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* تنسيق مخصص للشريط الجانبي */
.sidebar-avatar {
  background: linear-gradient(45deg, #006D57 30%, #005B48 90%);
  transition: transform 0.2s;
}

.sidebar-avatar:hover {
  transform: scale(1.05);
}

/* تنسيق مخصص للأزرار */
.MuiIconButton-root {
  transition: all 0.2s;
}

.MuiIconButton-root:hover {
  background-color: #f0f0f0 !important;
}

/* تحسينات الخط */
* {
  letter-spacing: -0.2px;
}

/* تنسيق الزر النشط في القائمة */
.MuiListItemButton-root.Mui-selected {
  position: relative;
}

.MuiListItemButton-root.Mui-selected::before {
  content: '';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background-color: white;
  border-radius: 2px;
}
