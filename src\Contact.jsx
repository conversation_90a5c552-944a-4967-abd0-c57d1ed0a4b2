import React, { useState } from 'react';
import {
  Typography,
  Container,
  Box,
  TextField,
  Grid,
  Paper,
  Alert
} from '@mui/material';
import MainButton from './MainButton';
import EmailIcon from '@mui/icons-material/Email';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import PhoneIcon from '@mui/icons-material/Phone';

const ContactInfo = ({ icon, title, text }) => (
  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
    <Box sx={{ mr: 2, color: 'primary.main' }}>{icon}</Box>
    <Box>
      <Typography variant="subtitle1" sx={{ fontWeight: 'bold' }}>{title}</Typography>
      <Typography variant="body2" color="text.secondary">{text}</Typography>
    </Box>
  </Box>
);

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [submitted, setSubmitted] = useState(false);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    setSubmitted(true);
    setFormData({ name: '', email: '', message: '' });
    setTimeout(() => setSubmitted(false), 5000);
  };

  return (
    <Box 
      id="contact" 
      sx={{ 
        py: 10,
        backgroundColor: '#f8f9fa'
      }}
    >
      <Container maxWidth="lg">
        <Typography 
          variant="h2" 
          component="h2" 
          align="center" 
          gutterBottom
          sx={{ 
            fontWeight: 'bold',
            color: 'primary.main',
            mb: 8
          }}
        >
          تواصل معنا
        </Typography>

        <Grid container spacing={4}>
          <Grid item xs={12} md={4}>
            <Paper 
              elevation={0} 
              sx={{ 
                p: 3, 
                height: '100%',
                backgroundColor: 'white',
                borderRadius: 4
              }}
            >
              <Typography variant="h6" gutterBottom sx={{ mb: 3, fontWeight: 'bold' }}>
                معلومات التواصل
              </Typography>
              
              <ContactInfo
                icon={<EmailIcon />}
                title="البريد الإلكتروني"
                text="<EMAIL>"
              />
              
              <ContactInfo
                icon={<PhoneIcon />}
                title="الهاتف"
                text="0123456789"
              />
              
              <ContactInfo
                icon={<LocationOnIcon />}
                title="العنوان"
                text="شارع النور، المدينة المنورة"
              />
            </Paper>
          </Grid>

          <Grid item xs={12} md={8}>
            <Paper 
              elevation={3} 
              sx={{ 
                p: 4,
                borderRadius: 4,
                boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
              }}
            >
              {submitted && (
                <Alert severity="success" sx={{ mb: 3 }}>
                  تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.
                </Alert>
              )}

              <form onSubmit={handleSubmit}>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      required
                      fullWidth
                      label="الاسم"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      required
                      fullWidth
                      label="البريد الإلكتروني"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleChange}
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      required
                      fullWidth
                      label="الرسالة"
                      name="message"
                      multiline
                      rows={4}
                      value={formData.message}
                      onChange={handleChange}
                      variant="outlined"
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <MainButton
                      type="submit"
                      sx={{
                        borderRadius: 8,
                        px: 4,
                        py: 1.5,
                        fontSize: '1.1rem'
                      }}
                    >
                      إرسال الرسالة
                    </MainButton>
                  </Grid>
                </Grid>
              </form>
            </Paper>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default Contact;
