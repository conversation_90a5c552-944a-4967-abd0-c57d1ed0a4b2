import React from 'react';
import { Card, CardContent, Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

const SimpleCard = styled(Card)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
  border: `1px solid ${theme.palette.divider}`,
  borderRadius: theme.shape.borderRadius,
  boxShadow: theme.shadows[1],
  transition: 'box-shadow 0.2s ease',
  '&:hover': {
    boxShadow: theme.shadows[2]
  }
}));

const IconContainer = styled(Box)(({ theme, iconColor }) => ({
  width: 48,
  height: 48,
  borderRadius: theme.shape.borderRadius,
  backgroundColor: `${iconColor}15`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(2)
}));

const StatCard = ({
  title,
  subtitle,
  value,
  icon,
  iconColor = '#5F6368',
  action,
  children,
  ...props
}) => {
  return (
    <SimpleCard {...props}>
      <CardContent sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ flex: 1 }}>
            {icon && (
              <IconContainer iconColor={iconColor}>
                {React.cloneElement(icon, {
                  sx: { fontSize: 24, color: iconColor }
                })}
              </IconContainer>
            )}

            {title && (
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 500,
                  color: 'text.primary',
                  mb: 0.5,
                  fontSize: '1rem'
                }}
              >
                {title}
              </Typography>
            )}

            {subtitle && (
              <Typography
                variant="body2"
                sx={{
                  color: 'text.secondary',
                  mb: 2,
                  fontSize: '0.8rem'
                }}
              >
                {subtitle}
              </Typography>
            )}

            {value && (
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 600,
                  color: 'text.primary',
                  fontSize: '1.5rem',
                  lineHeight: 1.2
                }}
              >
                {value}
              </Typography>
            )}
          </Box>

          {action && (
            <Box sx={{ ml: 2 }}>
              {action}
            </Box>
          )}
        </Box>

        {children}
      </CardContent>
    </SimpleCard>
  );
};

export default StatCard;
