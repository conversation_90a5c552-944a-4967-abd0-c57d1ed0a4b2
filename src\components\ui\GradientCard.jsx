import React from 'react';
import { Card, CardContent, Box, Typography, IconButton } from '@mui/material';
import { styled } from '@mui/material/styles';

const StyledCard = styled(Card)(({ theme, gradient, hover = true }) => ({
  background: gradient || 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
  border: '2px solid rgba(255, 255, 255, 0.2)',
  borderRadius: theme.spacing(2),
  backdropFilter: 'blur(10px)',
  boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: 'linear-gradient(90deg, #4CAF50, #2196F3, #FF9800, #9C27B0)',
    backgroundSize: '300% 100%',
    animation: 'gradient 3s ease infinite'
  },
  '&:hover': hover ? {
    transform: 'translateY(-8px) scale(1.02)',
    boxShadow: '0 20px 60px rgba(0, 0, 0, 0.15)',
    '&::before': {
      height: '6px'
    }
  } : {},
  '@keyframes gradient': {
    '0%': { backgroundPosition: '0% 50%' },
    '50%': { backgroundPosition: '100% 50%' },
    '100%': { backgroundPosition: '0% 50%' }
  }
}));

const IconContainer = styled(Box)(({ theme, iconColor }) => ({
  width: 64,
  height: 64,
  borderRadius: '50%',
  background: `linear-gradient(135deg, ${iconColor}20, ${iconColor}40)`,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  marginBottom: theme.spacing(2),
  position: 'relative',
  '&::after': {
    content: '""',
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: '50%',
    background: `linear-gradient(135deg, ${iconColor}10, transparent)`,
    animation: 'pulse 2s ease-in-out infinite'
  },
  '@keyframes pulse': {
    '0%': { transform: 'scale(1)', opacity: 1 },
    '50%': { transform: 'scale(1.1)', opacity: 0.7 },
    '100%': { transform: 'scale(1)', opacity: 1 }
  }
}));

const GradientCard = ({ 
  title, 
  subtitle, 
  value, 
  icon, 
  gradient, 
  iconColor = '#4CAF50',
  action,
  children,
  hover = true,
  ...props 
}) => {
  return (
    <StyledCard gradient={gradient} hover={hover} {...props}>
      <CardContent sx={{ p: 3, position: 'relative', zIndex: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 2 }}>
          <Box sx={{ flex: 1 }}>
            {icon && (
              <IconContainer iconColor={iconColor}>
                {React.cloneElement(icon, { 
                  sx: { fontSize: 32, color: iconColor } 
                })}
              </IconContainer>
            )}
            
            {title && (
              <Typography 
                variant="h6" 
                sx={{ 
                  fontWeight: 600, 
                  color: 'text.primary',
                  mb: 0.5,
                  lineHeight: 1.3
                }}
              >
                {title}
              </Typography>
            )}
            
            {subtitle && (
              <Typography 
                variant="body2" 
                sx={{ 
                  color: 'text.secondary',
                  mb: 1,
                  opacity: 0.8
                }}
              >
                {subtitle}
              </Typography>
            )}
            
            {value && (
              <Typography 
                variant="h4" 
                sx={{ 
                  fontWeight: 700,
                  background: `linear-gradient(135deg, ${iconColor}, ${iconColor}80)`,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  lineHeight: 1.2
                }}
              >
                {value}
              </Typography>
            )}
          </Box>
          
          {action && (
            <Box sx={{ ml: 2 }}>
              {action}
            </Box>
          )}
        </Box>
        
        {children}
      </CardContent>
    </StyledCard>
  );
};

export default GradientCard;
