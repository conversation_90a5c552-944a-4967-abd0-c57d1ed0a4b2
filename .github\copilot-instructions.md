<!-- Use this file to provide workspace-specific custom instructions to <PERSON><PERSON><PERSON>. For more details, visit https://code.visualstudio.com/docs/copilot/copilot-customization#_use-a-githubcopilotinstructionsmd-file -->

- هذا مشروع React احترافي باستخدام Vite وMUI وReact Router.
- يجب أن تكون جميع المكونات قابلة لإعادة الاستخدام.
- استخدم تصميمًا حديثًا ومتجاوبًا.
- أضف صفحات: الرئيسية، عن الموقع، تواصل، وخطأ 404.
- استخدم React Router للتنقل بين الصفحات.
- استخدم مكونات MUI في جميع الواجهات.
