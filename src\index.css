@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap');

:root {
  font-family: 'Tajawal', system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: rgba(0, 0, 0, 0.87);
  background-color: #ffffff;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* Smooth scrolling for sections */
section {
  scroll-margin-top: 64px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #005b48;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #004a3b;
}

/* Animation classes */
.fade-in {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive text utilities */
@media (max-width: 600px) {
  h1 {
    font-size: 2.5rem !important;
  }
  h2 {
    font-size: 2rem !important;
  }
  h3 {
    font-size: 1.75rem !important;
  }
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

/* MUI TextField customization */
.MuiOutlinedInput-root {
  border-radius: 8px !important;
}

/* Remove default outline for better aesthetics */
.MuiButtonBase-root:focus {
  outline: none !important;
}

/* Smooth transitions */
.MuiPaper-root {
  transition: box-shadow 0.3s ease-in-out !important;
}

/* Better button hover states */
.MuiButton-root:hover {
  transform: translateY(-1px);
  transition: transform 0.2s ease-in-out;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}
