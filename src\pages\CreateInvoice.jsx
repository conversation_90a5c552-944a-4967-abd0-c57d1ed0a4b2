import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Divider,
  Card,
  CardContent,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Print as PrintIcon,
  Search as SearchIcon,
  Close as CloseIcon,
  ShoppingCart as CartIcon,
  Receipt as ReceiptIcon
} from '@mui/icons-material';
import { useApp } from '../context/AppContext';
import { useNavigate } from 'react-router-dom';

const CreateInvoice = () => {
  const { state, addInvoice } = useApp();
  const navigate = useNavigate();

  // بيانات الفاتورة
  const [invoiceData, setInvoiceData] = useState({
    number: `INV-${Date.now()}`,
    date: new Date().toISOString().split('T')[0],
    time: new Date().toLocaleTimeString('ar-SA'),
    customer: '',
    customerPhone: '',
    customerAddress: '',
    type: 'بيع',
    paymentMethod: 'نقدي',
    notes: '',
    items: [],
    subtotal: 0,
    tax: 0,
    discount: 0,
    total: 0
  });

  // حالة البحث عن المنتجات
  const [productSearch, setProductSearch] = useState('');
  const [showProductDialog, setShowProductDialog] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [price, setPrice] = useState(0);

  // تحديث الحسابات عند تغيير الأصناف
  useEffect(() => {
    const subtotal = invoiceData.items.reduce((sum, item) => sum + item.total, 0);
    const tax = subtotal * 0.15; // ضريبة 15%
    const total = subtotal + tax - invoiceData.discount;

    setInvoiceData(prev => ({
      ...prev,
      subtotal,
      tax,
      total
    }));
  }, [invoiceData.items, invoiceData.discount]);

  // إضافة صنف للفاتورة
  const addItemToInvoice = () => {
    if (!selectedProduct || quantity <= 0) return;

    const newItem = {
      id: Date.now(),
      productId: selectedProduct.id,
      name: selectedProduct.name,
      code: selectedProduct.sku || selectedProduct.code,
      quantity: quantity,
      price: price || selectedProduct.salePrice,
      total: quantity * (price || selectedProduct.salePrice),
      unit: selectedProduct.unit || 'قطعة'
    };

    setInvoiceData(prev => ({
      ...prev,
      items: [...prev.items, newItem]
    }));

    // إعادة تعيين القيم
    setSelectedProduct(null);
    setQuantity(1);
    setPrice(0);
    setShowProductDialog(false);
  };

  // حذف صنف من الفاتورة
  const removeItem = (itemId) => {
    setInvoiceData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId)
    }));
  };

  // تحديث كمية صنف
  const updateItemQuantity = (itemId, newQuantity) => {
    if (newQuantity <= 0) return;

    setInvoiceData(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === itemId
          ? { ...item, quantity: newQuantity, total: newQuantity * item.price }
          : item
      )
    }));
  };

  // تحديث سعر صنف
  const updateItemPrice = (itemId, newPrice) => {
    if (newPrice < 0) return;

    setInvoiceData(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === itemId
          ? { ...item, price: newPrice, total: item.quantity * newPrice }
          : item
      )
    }));
  };

  // حفظ الفاتورة
  const saveInvoice = () => {
    if (invoiceData.items.length === 0) {
      alert('يرجى إضافة صنف واحد على الأقل');
      return;
    }

    if (!invoiceData.customer) {
      alert('يرجى اختيار العميل');
      return;
    }

    addInvoice(invoiceData);
    alert('تم حفظ الفاتورة بنجاح');
    navigate('/invoices');
  };

  // طباعة الفاتورة
  const printInvoice = () => {
    window.print();
  };

  // فلترة المنتجات للبحث
  const filteredProducts = state.products.filter(product =>
    product.name.toLowerCase().includes(productSearch.toLowerCase()) ||
    (product.code && product.code.toLowerCase().includes(productSearch.toLowerCase())) ||
    (product.sku && product.sku.toLowerCase().includes(productSearch.toLowerCase()))
  );

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', bgcolor: '#f5f7fa' }}>
      {/* شريط الأدوات العلوي */}
      <Paper sx={{ p: 2, mb: 0, bgcolor: '#005B48', color: 'white', borderRadius: 0 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <ReceiptIcon sx={{ fontSize: 32 }} />
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                إنشاء فاتورة جديدة
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                رقم الفاتورة: {invoiceData.number} | التاريخ: {new Date().toLocaleDateString('ar-SA')}
              </Typography>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="contained"
              size="small"
              startIcon={<SaveIcon />}
              onClick={saveInvoice}
              sx={{ bgcolor: 'white', color: '#005B48', '&:hover': { bgcolor: '#f5f5f5' } }}
            >
              حفظ
            </Button>
            <Button
              variant="outlined"
              size="small"
              startIcon={<PrintIcon />}
              onClick={printInvoice}
              sx={{ borderColor: 'white', color: 'white', '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' } }}
            >
              طباعة
            </Button>
            <Button
              variant="outlined"
              size="small"
              onClick={() => navigate('/invoices')}
              sx={{ borderColor: 'white', color: 'white', '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' } }}
            >
              إلغاء
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* المحتوى الرئيسي */}
      <Box sx={{ flex: 1, p: 2, overflow: 'auto' }}>

      <Grid container spacing={3}>
        {/* معلومات الفاتورة */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, color: '#005B48', fontWeight: 'bold' }}>
              معلومات الفاتورة
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="رقم الفاتورة"
                  value={invoiceData.number}
                  fullWidth
                  disabled
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="التاريخ"
                  type="date"
                  value={invoiceData.date}
                  onChange={(e) => setInvoiceData(prev => ({ ...prev, date: e.target.value }))}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>العميل</InputLabel>
                  <Select
                    value={invoiceData.customer}
                    onChange={(e) => {
                      const customer = state.customers.find(c => c.name === e.target.value);
                      setInvoiceData(prev => ({
                        ...prev,
                        customer: e.target.value,
                        customerPhone: customer?.phone || '',
                        customerAddress: customer?.address || ''
                      }));
                    }}
                    label="العميل"
                  >
                    {state.customers.map(customer => (
                      <MenuItem key={customer.id} value={customer.name}>
                        {customer.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel>طريقة الدفع</InputLabel>
                  <Select
                    value={invoiceData.paymentMethod}
                    onChange={(e) => setInvoiceData(prev => ({ ...prev, paymentMethod: e.target.value }))}
                    label="طريقة الدفع"
                  >
                    <MenuItem value="نقدي">نقدي</MenuItem>
                    <MenuItem value="بطاقة ائتمان">بطاقة ائتمان</MenuItem>
                    <MenuItem value="تحويل بنكي">تحويل بنكي</MenuItem>
                    <MenuItem value="آجل">آجل</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Paper>

          {/* إضافة الأصناف */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6" sx={{ color: '#005B48', fontWeight: 'bold' }}>
                أصناف الفاتورة
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setShowProductDialog(true)}
                sx={{ bgcolor: '#005B48', '&:hover': { bgcolor: '#004a3b' } }}
              >
                إضافة صنف
              </Button>
            </Box>

            {/* جدول الأصناف */}
            <TableContainer sx={{ border: '1px solid #e0e0e0', borderRadius: 1 }}>
              <Table size="small">
                <TableHead>
                  <TableRow sx={{ bgcolor: '#005B48' }}>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold', textAlign: 'center' }}>م</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>اسم الصنف</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold', textAlign: 'center' }}>نوع الصنف</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold', textAlign: 'center' }}>بحث</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold', textAlign: 'center' }}>كمية</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold', textAlign: 'center' }}>سعر</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold', textAlign: 'center' }}>قيمة</TableCell>
                    <TableCell sx={{ color: 'white', fontWeight: 'bold', textAlign: 'center' }}>إجراءات</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {invoiceData.items.map((item, index) => (
                    <TableRow key={item.id} sx={{ '&:nth-of-type(even)': { bgcolor: '#f9f9f9' } }}>
                      <TableCell sx={{ textAlign: 'center', fontWeight: 'bold' }}>{index + 1}</TableCell>
                      <TableCell sx={{ fontWeight: 'medium' }}>{item.name}</TableCell>
                      <TableCell sx={{ textAlign: 'center', color: '#666' }}>{item.unit || 'قطعة'}</TableCell>
                      <TableCell sx={{ textAlign: 'center', color: '#666', fontSize: '0.8rem' }}>{item.code || '-'}</TableCell>
                      <TableCell sx={{ textAlign: 'center' }}>
                        <TextField
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateItemQuantity(item.id, parseInt(e.target.value) || 0)}
                          size="small"
                          sx={{ width: 70 }}
                          inputProps={{ min: 1, style: { textAlign: 'center' } }}
                        />
                      </TableCell>
                      <TableCell sx={{ textAlign: 'center' }}>
                        <TextField
                          type="number"
                          value={item.price}
                          onChange={(e) => updateItemPrice(item.id, parseFloat(e.target.value) || 0)}
                          size="small"
                          sx={{ width: 90 }}
                          inputProps={{ style: { textAlign: 'center' } }}
                        />
                      </TableCell>
                      <TableCell sx={{ textAlign: 'center', fontWeight: 'bold', color: '#005B48' }}>
                        {item.total.toFixed(2)}
                      </TableCell>
                      <TableCell sx={{ textAlign: 'center' }}>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => removeItem(item.id)}
                          sx={{ '&:hover': { bgcolor: '#ffebee' } }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                  {invoiceData.items.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={8} sx={{ textAlign: 'center', py: 4 }}>
                        <Typography color="textSecondary">
                          لم يتم إضافة أي أصناف بعد
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                  {/* صف الإجمالي */}
                  {invoiceData.items.length > 0 && (
                    <TableRow sx={{ bgcolor: '#f0f0f0', borderTop: '2px solid #005B48' }}>
                      <TableCell colSpan={6} sx={{ textAlign: 'right', fontWeight: 'bold', fontSize: '1.1rem' }}>
                        الإجمالي:
                      </TableCell>
                      <TableCell sx={{ textAlign: 'center', fontWeight: 'bold', fontSize: '1.1rem', color: '#005B48' }}>
                        {invoiceData.subtotal.toFixed(2)}
                      </TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        {/* ملخص الفاتورة */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, mb: 2, bgcolor: '#f8f9fa', border: '1px solid #e0e0e0' }}>
            <Typography variant="h6" sx={{ mb: 2, color: '#005B48', fontWeight: 'bold', textAlign: 'center' }}>
              ملخص الفاتورة
            </Typography>

            <Box sx={{ bgcolor: 'white', p: 2, borderRadius: 1, border: '1px solid #e0e0e0' }}>
              <Grid container spacing={1}>
                <Grid item xs={6}>
                  <Typography variant="body2" sx={{ fontWeight: 'medium' }}>المجموع الفرعي:</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" sx={{ textAlign: 'right', fontWeight: 'bold' }}>
                    {invoiceData.subtotal.toFixed(2)} ر.س
                  </Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography variant="body2" sx={{ fontWeight: 'medium' }}>الضريبة (15%):</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" sx={{ textAlign: 'right', fontWeight: 'bold' }}>
                    {invoiceData.tax.toFixed(2)} ر.س
                  </Typography>
                </Grid>

                <Grid item xs={6}>
                  <Typography variant="body2" sx={{ fontWeight: 'medium' }}>الخصم:</Typography>
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    type="number"
                    value={invoiceData.discount}
                    onChange={(e) => setInvoiceData(prev => ({
                      ...prev,
                      discount: parseFloat(e.target.value) || 0
                    }))}
                    size="small"
                    sx={{ width: '100%' }}
                    inputProps={{ style: { textAlign: 'right' } }}
                  />
                </Grid>

                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }} />
                </Grid>

                <Grid item xs={6}>
                  <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#005B48' }}>
                    الإجمالي النهائي:
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="h6" sx={{ textAlign: 'right', fontWeight: 'bold', color: '#005B48' }}>
                    {invoiceData.total.toFixed(2)} ر.س
                  </Typography>
                </Grid>
              </Grid>
            </Box>

            <TextField
              label="ملاحظات"
              value={invoiceData.notes}
              onChange={(e) => setInvoiceData(prev => ({ ...prev, notes: e.target.value }))}
              fullWidth
              multiline
              rows={3}
              sx={{ mt: 2 }}
              placeholder="أدخل أي ملاحظات إضافية..."
            />
          </Paper>
        </Grid>
      </Grid>

      {/* نافذة اختيار المنتجات */}
      <Dialog
        open={showProductDialog}
        onClose={() => setShowProductDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle sx={{ bgcolor: '#005B48', color: 'white', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">اختيار المنتج</Typography>
          <IconButton onClick={() => setShowProductDialog(false)} sx={{ color: 'white' }}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {/* بحث المنتجات */}
          <Box sx={{ p: 2, borderBottom: '1px solid #eee' }}>
            <TextField
              fullWidth
              placeholder="بحث عن منتج..."
              value={productSearch}
              onChange={(e) => setProductSearch(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                )
              }}
            />
          </Box>

          {/* قائمة المنتجات */}
          <List sx={{ maxHeight: 400, overflow: 'auto' }}>
            {filteredProducts.map(product => (
              <ListItem key={product.id} disablePadding>
                <ListItemButton
                  onClick={() => {
                    setSelectedProduct(product);
                    setPrice(product.salePrice);
                  }}
                  selected={selectedProduct?.id === product.id}
                >
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                          {product.name}
                        </Typography>
                        <Chip
                          label={`${product.salePrice} ر.س`}
                          size="small"
                          color="primary"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="textSecondary">
                          الكود: {product.code || product.sku || 'غير محدد'}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          المخزون: {product.quantity} {product.unit || 'قطعة'}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItemButton>
              </ListItem>
            ))}
            {filteredProducts.length === 0 && (
              <ListItem>
                <ListItemText
                  primary={
                    <Typography sx={{ textAlign: 'center', py: 4, color: 'textSecondary' }}>
                      لا توجد منتجات مطابقة للبحث
                    </Typography>
                  }
                />
              </ListItem>
            )}
          </List>

          {/* تفاصيل المنتج المختار */}
          {selectedProduct && (
            <Box sx={{ p: 2, borderTop: '1px solid #eee', bgcolor: '#f9f9f9' }}>
              <Typography variant="h6" sx={{ mb: 2, color: '#005B48' }}>
                تفاصيل المنتج المختار
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    label="الكمية"
                    type="number"
                    value={quantity}
                    onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                    fullWidth
                    inputProps={{ min: 1, max: selectedProduct.quantity }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    label="السعر"
                    type="number"
                    value={price}
                    onChange={(e) => setPrice(parseFloat(e.target.value) || 0)}
                    fullWidth
                    InputProps={{
                      endAdornment: <InputAdornment position="end">ر.س</InputAdornment>
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2, bgcolor: 'white', borderRadius: 1 }}>
                    <Typography variant="body1">
                      الإجمالي: <strong>{(quantity * price).toFixed(2)} ر.س</strong>
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<CartIcon />}
                      onClick={addItemToInvoice}
                      disabled={!selectedProduct || quantity <= 0 || price <= 0}
                      sx={{ bgcolor: '#005B48', '&:hover': { bgcolor: '#004a3b' } }}
                    >
                      إضافة للفاتورة
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
      </Dialog>
      </Box>
    </Box>
  );
};

export default CreateInvoice;
