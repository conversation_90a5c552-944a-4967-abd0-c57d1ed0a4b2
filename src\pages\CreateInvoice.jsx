import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  TextField,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Divider,
  Card,
  CardContent,
  InputAdornment,
  Dialog,
  DialogTitle,
  DialogContent,
  List,
  ListItem,
  ListItemText,
  ListItemButton,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Print as PrintIcon,
  Search as SearchIcon,
  Close as CloseIcon,
  ShoppingCart as CartIcon,
  Receipt as ReceiptIcon
} from '@mui/icons-material';
import { useApp } from '../context/AppContext';
import { useNavigate } from 'react-router-dom';

const CreateInvoice = () => {
  const { state, addInvoice } = useApp();
  const navigate = useNavigate();

  // بيانات الفاتورة
  const [invoiceData, setInvoiceData] = useState({
    number: `INV-${Date.now()}`,
    date: new Date().toISOString().split('T')[0],
    time: new Date().toLocaleTimeString('ar-SA'),
    customer: '',
    customerPhone: '',
    customerAddress: '',
    type: 'بيع',
    paymentMethod: 'نقدي',
    notes: '',
    items: [],
    subtotal: 0,
    tax: 0,
    discount: 0,
    total: 0
  });

  // حالة البحث عن المنتجات
  const [productSearch, setProductSearch] = useState('');
  const [showProductDialog, setShowProductDialog] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [quantity, setQuantity] = useState(1);
  const [price, setPrice] = useState(0);

  // تحديث الحسابات عند تغيير الأصناف
  useEffect(() => {
    const subtotal = invoiceData.items.reduce((sum, item) => sum + item.total, 0);
    const tax = subtotal * 0.15; // ضريبة 15%
    const total = subtotal + tax - invoiceData.discount;

    setInvoiceData(prev => ({
      ...prev,
      subtotal,
      tax,
      total
    }));
  }, [invoiceData.items, invoiceData.discount]);

  // إضافة صنف للفاتورة
  const addItemToInvoice = () => {
    if (!selectedProduct || quantity <= 0) return;

    const newItem = {
      id: Date.now(),
      productId: selectedProduct.id,
      name: selectedProduct.name,
      code: selectedProduct.sku || selectedProduct.code,
      quantity: quantity,
      price: price || selectedProduct.salePrice,
      total: quantity * (price || selectedProduct.salePrice),
      unit: selectedProduct.unit || 'قطعة'
    };

    setInvoiceData(prev => ({
      ...prev,
      items: [...prev.items, newItem]
    }));

    // إعادة تعيين القيم
    setSelectedProduct(null);
    setQuantity(1);
    setPrice(0);
    setShowProductDialog(false);
  };

  // حذف صنف من الفاتورة
  const removeItem = (itemId) => {
    setInvoiceData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.id !== itemId)
    }));
  };

  // تحديث كمية صنف
  const updateItemQuantity = (itemId, newQuantity) => {
    if (newQuantity <= 0) return;

    setInvoiceData(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === itemId
          ? { ...item, quantity: newQuantity, total: newQuantity * item.price }
          : item
      )
    }));
  };

  // تحديث سعر صنف
  const updateItemPrice = (itemId, newPrice) => {
    if (newPrice < 0) return;

    setInvoiceData(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.id === itemId
          ? { ...item, price: newPrice, total: item.quantity * newPrice }
          : item
      )
    }));
  };

  // حفظ الفاتورة
  const saveInvoice = () => {
    if (invoiceData.items.length === 0) {
      alert('يرجى إضافة صنف واحد على الأقل');
      return;
    }

    if (!invoiceData.customer) {
      alert('يرجى اختيار العميل');
      return;
    }

    addInvoice(invoiceData);
    alert('تم حفظ الفاتورة بنجاح');
    navigate('/invoices');
  };

  // طباعة الفاتورة
  const printInvoice = () => {
    window.print();
  };

  // فلترة المنتجات للبحث
  const filteredProducts = state.products.filter(product =>
    product.name.toLowerCase().includes(productSearch.toLowerCase()) ||
    (product.code && product.code.toLowerCase().includes(productSearch.toLowerCase())) ||
    (product.sku && product.sku.toLowerCase().includes(productSearch.toLowerCase()))
  );

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', bgcolor: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)' }}>
      {/* شريط الأدوات العلوي المحسن */}
      <Paper
        elevation={4}
        sx={{
          p: 2,
          mb: 0,
          background: 'linear-gradient(135deg, #005B48 0%, #00796B 100%)',
          color: 'white',
          borderRadius: 0,
          boxShadow: '0 4px 20px rgba(0,91,72,0.3)'
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box
              sx={{
                p: 1.5,
                borderRadius: '50%',
                bgcolor: 'rgba(255,255,255,0.2)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <ReceiptIcon sx={{ fontSize: 28 }} />
            </Box>
            <Box>
              <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                إنشاء فاتورة جديدة
              </Typography>
              <Box sx={{ display: 'flex', gap: 3, opacity: 0.9 }}>
                <Typography variant="body2">
                  📄 رقم الفاتورة: {invoiceData.number}
                </Typography>
                <Typography variant="body2">
                  📅 التاريخ: {new Date().toLocaleDateString('ar-SA')}
                </Typography>
                <Typography variant="body2">
                  🕐 الوقت: {new Date().toLocaleTimeString('ar-SA')}
                </Typography>
              </Box>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="contained"
              size="medium"
              startIcon={<SaveIcon />}
              onClick={saveInvoice}
              sx={{
                bgcolor: 'white',
                color: '#005B48',
                fontWeight: 'bold',
                px: 3,
                borderRadius: 2,
                boxShadow: '0 2px 10px rgba(255,255,255,0.3)',
                '&:hover': {
                  bgcolor: '#f5f5f5',
                  transform: 'translateY(-1px)',
                  boxShadow: '0 4px 15px rgba(255,255,255,0.4)'
                },
                transition: 'all 0.2s ease'
              }}
            >
              حفظ الفاتورة
            </Button>
            <Button
              variant="outlined"
              size="medium"
              startIcon={<PrintIcon />}
              onClick={printInvoice}
              sx={{
                borderColor: 'white',
                color: 'white',
                borderWidth: 2,
                px: 3,
                borderRadius: 2,
                '&:hover': {
                  bgcolor: 'rgba(255,255,255,0.1)',
                  borderColor: 'white',
                  transform: 'translateY(-1px)'
                },
                transition: 'all 0.2s ease'
              }}
            >
              طباعة
            </Button>
            <Button
              variant="text"
              size="medium"
              onClick={() => navigate('/invoices')}
              sx={{
                color: 'white',
                px: 3,
                borderRadius: 2,
                '&:hover': {
                  bgcolor: 'rgba(255,255,255,0.1)',
                  transform: 'translateY(-1px)'
                },
                transition: 'all 0.2s ease'
              }}
            >
              إلغاء
            </Button>
          </Box>
        </Box>
      </Paper>

      {/* المحتوى الرئيسي */}
      <Box sx={{ flex: 1, p: 2, overflow: 'auto' }}>

      <Grid container spacing={3}>
        {/* معلومات الفاتورة المحسنة */}
        <Grid item xs={12} md={8}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              mb: 3,
              borderRadius: 3,
              background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
              border: '1px solid rgba(0,91,72,0.1)',
              boxShadow: '0 8px 32px rgba(0,91,72,0.1)'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
              <Box
                sx={{
                  p: 1,
                  borderRadius: 2,
                  bgcolor: 'rgba(0,91,72,0.1)',
                  mr: 2,
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                <ReceiptIcon sx={{ color: '#005B48', fontSize: 24 }} />
              </Box>
              <Typography variant="h6" sx={{ color: '#005B48', fontWeight: 'bold' }}>
                📋 معلومات الفاتورة
              </Typography>
            </Box>

            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="🔢 رقم الفاتورة"
                  value={invoiceData.number}
                  fullWidth
                  disabled
                  variant="outlined"
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      bgcolor: '#f8f9fa',
                      borderRadius: 2,
                      '& fieldset': {
                        borderColor: 'rgba(0,91,72,0.2)',
                      }
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="📅 التاريخ"
                  type="date"
                  value={invoiceData.date}
                  onChange={(e) => setInvoiceData(prev => ({ ...prev, date: e.target.value }))}
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2,
                      '& fieldset': {
                        borderColor: 'rgba(0,91,72,0.2)',
                      },
                      '&:hover fieldset': {
                        borderColor: '#005B48',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#005B48',
                      }
                    }
                  }}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: '#666' }}>👤 العميل</InputLabel>
                  <Select
                    value={invoiceData.customer}
                    onChange={(e) => {
                      const customer = state.customers.find(c => c.name === e.target.value);
                      setInvoiceData(prev => ({
                        ...prev,
                        customer: e.target.value,
                        customerPhone: customer?.phone || '',
                        customerAddress: customer?.address || ''
                      }));
                    }}
                    label="👤 العميل"
                    sx={{
                      borderRadius: 2,
                      '& fieldset': {
                        borderColor: 'rgba(0,91,72,0.2)',
                      },
                      '&:hover fieldset': {
                        borderColor: '#005B48',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#005B48',
                      }
                    }}
                  >
                    {state.customers.map(customer => (
                      <MenuItem key={customer.id} value={customer.name}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography>{customer.name}</Typography>
                          <Chip
                            label={customer.phone}
                            size="small"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem' }}
                          />
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel sx={{ color: '#666' }}>💳 طريقة الدفع</InputLabel>
                  <Select
                    value={invoiceData.paymentMethod}
                    onChange={(e) => setInvoiceData(prev => ({ ...prev, paymentMethod: e.target.value }))}
                    label="💳 طريقة الدفع"
                    sx={{
                      borderRadius: 2,
                      '& fieldset': {
                        borderColor: 'rgba(0,91,72,0.2)',
                      },
                      '&:hover fieldset': {
                        borderColor: '#005B48',
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: '#005B48',
                      }
                    }}
                  >
                    <MenuItem value="نقدي">💵 نقدي</MenuItem>
                    <MenuItem value="بطاقة ائتمان">💳 بطاقة ائتمان</MenuItem>
                    <MenuItem value="تحويل بنكي">🏦 تحويل بنكي</MenuItem>
                    <MenuItem value="آجل">📅 آجل</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Paper>

          {/* إضافة الأصناف المحسنة */}
          <Paper
            elevation={3}
            sx={{
              p: 3,
              mb: 3,
              borderRadius: 3,
              background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
              border: '1px solid rgba(0,91,72,0.1)',
              boxShadow: '0 8px 32px rgba(0,91,72,0.1)'
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box
                  sx={{
                    p: 1,
                    borderRadius: 2,
                    bgcolor: 'rgba(0,91,72,0.1)',
                    mr: 2,
                    display: 'flex',
                    alignItems: 'center'
                  }}
                >
                  <CartIcon sx={{ color: '#005B48', fontSize: 24 }} />
                </Box>
                <Box>
                  <Typography variant="h6" sx={{ color: '#005B48', fontWeight: 'bold' }}>
                    🛒 أصناف الفاتورة
                  </Typography>
                  <Typography variant="body2" sx={{ color: '#666', mt: 0.5 }}>
                    إجمالي الأصناف: {invoiceData.items.length} | المجموع: {invoiceData.subtotal.toFixed(2)} ر.س
                  </Typography>
                </Box>
              </Box>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setShowProductDialog(true)}
                sx={{
                  bgcolor: '#005B48',
                  borderRadius: 3,
                  px: 3,
                  py: 1.5,
                  fontWeight: 'bold',
                  boxShadow: '0 4px 15px rgba(0,91,72,0.3)',
                  '&:hover': {
                    bgcolor: '#004a3b',
                    transform: 'translateY(-2px)',
                    boxShadow: '0 6px 20px rgba(0,91,72,0.4)'
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                ➕ إضافة صنف
              </Button>
            </Box>

            {/* جدول الأصناف المحسن */}
            <TableContainer
              sx={{
                border: '2px solid rgba(0,91,72,0.1)',
                borderRadius: 3,
                overflow: 'hidden',
                boxShadow: '0 4px 20px rgba(0,91,72,0.1)'
              }}
            >
              <Table size="small">
                <TableHead>
                  <TableRow sx={{
                    background: 'linear-gradient(135deg, #005B48 0%, #00796B 100%)',
                    '& .MuiTableCell-root': {
                      borderBottom: 'none'
                    }
                  }}>
                    <TableCell sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      textAlign: 'center',
                      fontSize: '0.9rem',
                      py: 2
                    }}>
                      #️⃣
                    </TableCell>
                    <TableCell sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      fontSize: '0.9rem',
                      py: 2
                    }}>
                      📦 اسم الصنف
                    </TableCell>
                    <TableCell sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      textAlign: 'center',
                      fontSize: '0.9rem',
                      py: 2
                    }}>
                      🏷️ النوع
                    </TableCell>
                    <TableCell sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      textAlign: 'center',
                      fontSize: '0.9rem',
                      py: 2
                    }}>
                      🔍 الكود
                    </TableCell>
                    <TableCell sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      textAlign: 'center',
                      fontSize: '0.9rem',
                      py: 2
                    }}>
                      📊 الكمية
                    </TableCell>
                    <TableCell sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      textAlign: 'center',
                      fontSize: '0.9rem',
                      py: 2
                    }}>
                      💰 السعر
                    </TableCell>
                    <TableCell sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      textAlign: 'center',
                      fontSize: '0.9rem',
                      py: 2
                    }}>
                      💵 القيمة
                    </TableCell>
                    <TableCell sx={{
                      color: 'white',
                      fontWeight: 'bold',
                      textAlign: 'center',
                      fontSize: '0.9rem',
                      py: 2
                    }}>
                      ⚙️ إجراءات
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {invoiceData.items.map((item, index) => (
                    <TableRow
                      key={item.id}
                      sx={{
                        '&:nth-of-type(even)': { bgcolor: 'rgba(0,91,72,0.02)' },
                        '&:hover': {
                          bgcolor: 'rgba(0,91,72,0.05)',
                          transform: 'scale(1.001)',
                          boxShadow: '0 2px 8px rgba(0,91,72,0.1)'
                        },
                        transition: 'all 0.2s ease',
                        cursor: 'pointer'
                      }}
                    >
                      <TableCell sx={{
                        textAlign: 'center',
                        fontWeight: 'bold',
                        color: '#005B48',
                        fontSize: '1rem'
                      }}>
                        {index + 1}
                      </TableCell>
                      <TableCell sx={{
                        fontWeight: 'medium',
                        color: '#333',
                        fontSize: '0.9rem'
                      }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Box
                            sx={{
                              width: 8,
                              height: 8,
                              borderRadius: '50%',
                              bgcolor: '#4caf50'
                            }}
                          />
                          {item.name}
                        </Box>
                      </TableCell>
                      <TableCell sx={{
                        textAlign: 'center',
                        color: '#666',
                        fontSize: '0.8rem'
                      }}>
                        <Chip
                          label={item.unit || 'قطعة'}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.7rem' }}
                        />
                      </TableCell>
                      <TableCell sx={{
                        textAlign: 'center',
                        color: '#666',
                        fontSize: '0.8rem',
                        fontFamily: 'monospace'
                      }}>
                        {item.code || '-'}
                      </TableCell>
                      <TableCell sx={{ textAlign: 'center' }}>
                        <TextField
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateItemQuantity(item.id, parseInt(e.target.value) || 0)}
                          size="small"
                          sx={{
                            width: 80,
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 2,
                              '& fieldset': {
                                borderColor: 'rgba(0,91,72,0.2)',
                              },
                              '&:hover fieldset': {
                                borderColor: '#005B48',
                              },
                              '&.Mui-focused fieldset': {
                                borderColor: '#005B48',
                              }
                            }
                          }}
                          inputProps={{ min: 1, style: { textAlign: 'center', fontWeight: 'bold' } }}
                        />
                      </TableCell>
                      <TableCell sx={{ textAlign: 'center' }}>
                        <TextField
                          type="number"
                          value={item.price}
                          onChange={(e) => updateItemPrice(item.id, parseFloat(e.target.value) || 0)}
                          size="small"
                          sx={{
                            width: 100,
                            '& .MuiOutlinedInput-root': {
                              borderRadius: 2,
                              '& fieldset': {
                                borderColor: 'rgba(0,91,72,0.2)',
                              },
                              '&:hover fieldset': {
                                borderColor: '#005B48',
                              },
                              '&.Mui-focused fieldset': {
                                borderColor: '#005B48',
                              }
                            }
                          }}
                          inputProps={{ style: { textAlign: 'center', fontWeight: 'bold' } }}
                        />
                      </TableCell>
                      <TableCell sx={{
                        textAlign: 'center',
                        fontWeight: 'bold',
                        color: '#005B48',
                        fontSize: '1rem',
                        bgcolor: 'rgba(0,91,72,0.05)',
                        borderRadius: 1
                      }}>
                        💰 {item.total.toFixed(2)}
                      </TableCell>
                      <TableCell sx={{ textAlign: 'center' }}>
                        <IconButton
                          size="small"
                          color="error"
                          onClick={() => removeItem(item.id)}
                          sx={{
                            borderRadius: 2,
                            '&:hover': {
                              bgcolor: '#ffebee',
                              transform: 'scale(1.1)'
                            },
                            transition: 'all 0.2s ease'
                          }}
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                  {invoiceData.items.length === 0 && (
                    <TableRow>
                      <TableCell colSpan={8} sx={{ textAlign: 'center', py: 4 }}>
                        <Typography color="textSecondary">
                          لم يتم إضافة أي أصناف بعد
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                  {/* صف الإجمالي */}
                  {invoiceData.items.length > 0 && (
                    <TableRow sx={{ bgcolor: '#f0f0f0', borderTop: '2px solid #005B48' }}>
                      <TableCell colSpan={6} sx={{ textAlign: 'right', fontWeight: 'bold', fontSize: '1.1rem' }}>
                        الإجمالي:
                      </TableCell>
                      <TableCell sx={{ textAlign: 'center', fontWeight: 'bold', fontSize: '1.1rem', color: '#005B48' }}>
                        {invoiceData.subtotal.toFixed(2)}
                      </TableCell>
                      <TableCell></TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>

        {/* ملخص الفاتورة المحسن */}
        <Grid item xs={12} md={4}>
          <Box sx={{ position: 'sticky', top: 20 }}>
            <Paper
              elevation={4}
              sx={{
                p: 3,
                mb: 2,
                borderRadius: 4,
                background: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)',
                border: '2px solid rgba(0,91,72,0.1)',
                boxShadow: '0 12px 40px rgba(0,91,72,0.15)'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                <Box
                  sx={{
                    p: 1.5,
                    borderRadius: 3,
                    background: 'linear-gradient(135deg, #005B48 0%, #00796B 100%)',
                    mr: 2,
                    display: 'flex',
                    alignItems: 'center'
                  }}
                >
                  <ReceiptIcon sx={{ color: 'white', fontSize: 24 }} />
                </Box>
                <Typography variant="h6" sx={{ color: '#005B48', fontWeight: 'bold' }}>
                  📊 ملخص الفاتورة
                </Typography>
              </Box>

              <Box sx={{
                bgcolor: 'white',
                p: 3,
                borderRadius: 3,
                border: '1px solid rgba(0,91,72,0.1)',
                boxShadow: '0 4px 20px rgba(0,91,72,0.05)'
              }}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Box sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 2,
                      borderRadius: 2,
                      bgcolor: 'rgba(0,91,72,0.05)'
                    }}>
                      <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center', gap: 1 }}>
                        📦 المجموع الفرعي:
                      </Typography>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#005B48' }}>
                        {invoiceData.subtotal.toFixed(2)} ر.س
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 2,
                      borderRadius: 2,
                      bgcolor: 'rgba(255,193,7,0.1)'
                    }}>
                      <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center', gap: 1 }}>
                        🧾 الضريبة (15%):
                      </Typography>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', color: '#ff9800' }}>
                        {invoiceData.tax.toFixed(2)} ر.س
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 2,
                      borderRadius: 2,
                      bgcolor: 'rgba(244,67,54,0.1)'
                    }}>
                      <Typography variant="body1" sx={{ fontWeight: 'medium', display: 'flex', alignItems: 'center', gap: 1 }}>
                        💸 الخصم:
                      </Typography>
                      <TextField
                        type="number"
                        value={invoiceData.discount}
                        onChange={(e) => setInvoiceData(prev => ({
                          ...prev,
                          discount: parseFloat(e.target.value) || 0
                        }))}
                        size="small"
                        sx={{
                          width: 120,
                          '& .MuiOutlinedInput-root': {
                            borderRadius: 2,
                            bgcolor: 'white',
                            '& fieldset': {
                              borderColor: 'rgba(244,67,54,0.3)',
                            },
                            '&:hover fieldset': {
                              borderColor: '#f44336',
                            },
                            '&.Mui-focused fieldset': {
                              borderColor: '#f44336',
                            }
                          }
                        }}
                        inputProps={{ style: { textAlign: 'center', fontWeight: 'bold' } }}
                        InputProps={{
                          endAdornment: <InputAdornment position="end">ر.س</InputAdornment>
                        }}
                      />
                    </Box>
                  </Grid>

                  <Grid item xs={12}>
                    <Divider sx={{ my: 2, borderColor: 'rgba(0,91,72,0.2)' }} />
                  </Grid>

                  <Grid item xs={12}>
                    <Box sx={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      p: 3,
                      borderRadius: 3,
                      background: 'linear-gradient(135deg, #005B48 0%, #00796B 100%)',
                      color: 'white',
                      boxShadow: '0 6px 20px rgba(0,91,72,0.3)'
                    }}>
                      <Typography variant="h6" sx={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: 1 }}>
                        💰 الإجمالي النهائي:
                      </Typography>
                      <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                        {invoiceData.total.toFixed(2)} ر.س
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              </Box>

              <TextField
                label="📝 ملاحظات"
                value={invoiceData.notes}
                onChange={(e) => setInvoiceData(prev => ({ ...prev, notes: e.target.value }))}
                fullWidth
                multiline
                rows={4}
                sx={{
                  mt: 3,
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 3,
                    '& fieldset': {
                      borderColor: 'rgba(0,91,72,0.2)',
                    },
                    '&:hover fieldset': {
                      borderColor: '#005B48',
                    },
                    '&.Mui-focused fieldset': {
                      borderColor: '#005B48',
                    }
                  }
                }}
                placeholder="أدخل أي ملاحظات إضافية للفاتورة..."
              />
            </Paper>
          </Box>
        </Grid>
      </Grid>

      {/* نافذة اختيار المنتجات المحسنة */}
      <Dialog
        open={showProductDialog}
        onClose={() => setShowProductDialog(false)}
        maxWidth="lg"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 4,
            overflow: 'hidden',
            boxShadow: '0 20px 60px rgba(0,91,72,0.3)'
          }
        }}
      >
        <DialogTitle sx={{
          background: 'linear-gradient(135deg, #005B48 0%, #00796B 100%)',
          color: 'white',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          p: 3
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box
              sx={{
                p: 1,
                borderRadius: '50%',
                bgcolor: 'rgba(255,255,255,0.2)',
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <SearchIcon sx={{ fontSize: 24 }} />
            </Box>
            <Box>
              <Typography variant="h6" sx={{ fontWeight: 'bold' }}>
                🔍 اختيار المنتج
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.9 }}>
                ابحث واختر المنتج المطلوب إضافته للفاتورة
              </Typography>
            </Box>
          </Box>
          <IconButton
            onClick={() => setShowProductDialog(false)}
            sx={{
              color: 'white',
              bgcolor: 'rgba(255,255,255,0.1)',
              '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' }
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          {/* بحث المنتجات */}
          <Box sx={{ p: 2, borderBottom: '1px solid #eee' }}>
            <TextField
              fullWidth
              placeholder="بحث عن منتج..."
              value={productSearch}
              onChange={(e) => setProductSearch(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                )
              }}
            />
          </Box>

          {/* قائمة المنتجات */}
          <List sx={{ maxHeight: 400, overflow: 'auto' }}>
            {filteredProducts.map(product => (
              <ListItem key={product.id} disablePadding>
                <ListItemButton
                  onClick={() => {
                    setSelectedProduct(product);
                    setPrice(product.salePrice);
                  }}
                  selected={selectedProduct?.id === product.id}
                >
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                          {product.name}
                        </Typography>
                        <Chip
                          label={`${product.salePrice} ر.س`}
                          size="small"
                          color="primary"
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="textSecondary">
                          الكود: {product.code || product.sku || 'غير محدد'}
                        </Typography>
                        <Typography variant="body2" color="textSecondary">
                          المخزون: {product.quantity} {product.unit || 'قطعة'}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItemButton>
              </ListItem>
            ))}
            {filteredProducts.length === 0 && (
              <ListItem>
                <ListItemText
                  primary={
                    <Typography sx={{ textAlign: 'center', py: 4, color: 'textSecondary' }}>
                      لا توجد منتجات مطابقة للبحث
                    </Typography>
                  }
                />
              </ListItem>
            )}
          </List>

          {/* تفاصيل المنتج المختار */}
          {selectedProduct && (
            <Box sx={{ p: 2, borderTop: '1px solid #eee', bgcolor: '#f9f9f9' }}>
              <Typography variant="h6" sx={{ mb: 2, color: '#005B48' }}>
                تفاصيل المنتج المختار
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    label="الكمية"
                    type="number"
                    value={quantity}
                    onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                    fullWidth
                    inputProps={{ min: 1, max: selectedProduct.quantity }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    label="السعر"
                    type="number"
                    value={price}
                    onChange={(e) => setPrice(parseFloat(e.target.value) || 0)}
                    fullWidth
                    InputProps={{
                      endAdornment: <InputAdornment position="end">ر.س</InputAdornment>
                    }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2, bgcolor: 'white', borderRadius: 1 }}>
                    <Typography variant="body1">
                      الإجمالي: <strong>{(quantity * price).toFixed(2)} ر.س</strong>
                    </Typography>
                    <Button
                      variant="contained"
                      startIcon={<CartIcon />}
                      onClick={addItemToInvoice}
                      disabled={!selectedProduct || quantity <= 0 || price <= 0}
                      sx={{ bgcolor: '#005B48', '&:hover': { bgcolor: '#004a3b' } }}
                    >
                      إضافة للفاتورة
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
      </Dialog>
      </Box>
    </Box>
  );
};

export default CreateInvoice;
